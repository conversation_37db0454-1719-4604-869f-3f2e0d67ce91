<?xml version="1.0" encoding="UTF-8"?>
<!-- ***************************************** -->
<!-- This file was automatically generated by tools designed and maintained by Leutron Vision, Matrox and Groget. -->
<!-- Copyright (C) 2007-2013 Leutron Vision, Matrox, Groget. -->
<!-- You can freely use and modify the file without restrictions. -->
<!-- The file is provided as is, without any warranty. Bug reports and other feedback is appreciated. -->
<!-- ***************************************** -->
<!--   -->
<!-- ***************************************** -->
<!-- Version and date information: -->
<!-- SFNC type: GenTL -->
<!-- SFNC version: 1.0.0 -->
<!-- SFNC release date: 2013-05-06 -->
<!-- TXT extractor version: 1.4.1 -->
<!-- TXT extraction date: 2013-05-06 -->
<!-- XML generator version: 2.0 -->
<!-- XML generation date: 2013-05-06 -->
<!-- ***************************************** -->
<RegisterDescription xmlns="http://www.genicam.org/GenApi/Version_1_1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.genicam.org/GenApi/Version_1_1 http://www.genicam.org/GenApi/GenApiSchema_Version_1_1.xsd" ModelName="SFNC_Camera" VendorName="Generic" ToolTip="Reference SFNC camera XML (autogenerated)" StandardNameSpace="None" SchemaMajorVersion="1" SchemaMinorVersion="1" SchemaSubMinorVersion="0" MajorVersion="1" MinorVersion="0" SubMinorVersion="0" ProductGuid="11111111-**************-************" VersionGuid="AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE">
  <!-- ***************************************** -->
  <!-- RootCategory -->
  <!-- ***************************************** -->
  <Group Comment="RootCategory">
    <Category Name="Root" NameSpace="Standard">
      <!-- Mandatory element -->
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>StreamInformation</pFeature>
      <pFeature>BufferHandlingControl</pFeature>
	  <pFeature>StreamDiagnostics</pFeature>
    </Category>
    <Port Name="Device" NameSpace="Standard"></Port>
  </Group>
  <!-- ***************************************** -->
  <!-- SubCategories -->
  <!-- ***************************************** -->
  <Group Comment="SubCategories">
    <Category Name="StreamInformation" NameSpace="Standard">
      <!-- Recommended element -->
      <ToolTip>Category that contains all stream information features of the data stream module.</ToolTip>
      <Description>Category that contains all stream information features of the data stream module.</Description>
      <DisplayName>Stream Information</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>StreamID</pFeature>
      <pFeature>StreamType</pFeature>
	  <pFeature>StreamTotalBufferCount</pFeature>
    </Category>
    <Category Name="BufferHandlingControl" NameSpace="Standard">
      <!-- Recommended element -->
      <ToolTip>Contains all features of the data stream module that control the used buffers.</ToolTip>
      <Description>Contains all features of the data stream module that control the used buffers.</Description>
      <DisplayName>Buffer Handling Control</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pFeature>StreamDefaultBufferCount</pFeature>
      <pFeature>StreamDefaultBufferCountMax</pFeature>
	  <pFeature>StreamDefaultBufferCountMode</pFeature>
      <!--<pFeature>StreamAnnouncedBufferCount</pFeature>-->
      <pFeature>StreamBufferHandlingMode</pFeature>
      <pFeature>StreamCRCCheckEnable</pFeature>
      <!--<pFeature>StreamAnnounceBufferMinimum</pFeature>
      <pFeature>StreamDeliveredFrameCount</pFeature>
      <pFeature>StreamLostFrameCount</pFeature>
      <pFeature>StreamInputBufferCount</pFeature>
      <pFeature>StreamOutputBufferCount</pFeature>
      <pFeature>StreamStartedFrameCount</pFeature>
      <pFeature>PayloadSize</pFeature>
      <pFeature>StreamIsGrabbing</pFeature>
      <pFeature>StreamChunkCountMaximum</pFeature>
      <pFeature>StreamBufferAlignment</pFeature>-->
	  <pFeature>StreamBlockTransferSize</pFeature>
    </Category>
	<Category Name="StreamDiagnostics" NameSpace="Standard">
		<ToolTip>Category that contains all diagnostic features of the data stream module.</ToolTip>
		<Description>Category that contains all diagnostic features of the data stream module.</Description>
		<DisplayName>Stream Diagnostics</DisplayName>
		<Visibility>Beginner</Visibility>
		<ImposedAccessMode>RO</ImposedAccessMode>
		<pFeature>StreamFailedBufferCount</pFeature>
		<pFeature>StreamBufferUnderrunCount</pFeature>
    </Category>
  </Group>
  <!-- ***************************************** -->
  <!-- StreamInformation -->
  <!-- ***************************************** -->
  <Group Comment="StreamInformation">
    <StringReg Name="StreamID" NameSpace="Standard">
      <ToolTip>Device unique ID for the data stream, e.</ToolTip>
      <Description>Device unique ID for the data stream, e.g. a GUID.</Description>
      <DisplayName>Stream ID  </DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x0000</Address>
      <Length>250</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>

    <Enumeration Name="StreamType" NameSpace="Standard">
		<ToolTip>Stream type of the device.</ToolTip>
		<Description>Stream type of the device.</Description>
        <DisplayName>Stream Type</DisplayName>
        <Visibility>Expert</Visibility>
		<ImposedAccessMode>RO</ImposedAccessMode>
		<EnumEntry Name="Mixed" NameSpace="Standard">
			<ToolTip>Stream Type - Mixed.</ToolTip>
			<Description>Stream Type - Mixed</Description>
			<DisplayName>Mixed</DisplayName>
			<Value>0</Value>
		</EnumEntry>
		<EnumEntry Name="Custom" NameSpace="Standard">
			<ToolTip>Stream Type - Custom.</ToolTip>
			<Description>Stream Type - Custom</Description>
			<DisplayName>Custom</DisplayName>
			<Value>1</Value>
		</EnumEntry>
		<EnumEntry Name="GEV" NameSpace="Standard">
			<ToolTip>Stream Type - GEV.</ToolTip>
			<Description>Stream Type - GEV</Description>
			<DisplayName>GEV</DisplayName>
			<Value>2</Value>
		</EnumEntry>
		<EnumEntry Name="CL" NameSpace="Standard">
			<ToolTip>Stream Type - CL.</ToolTip>
			<Description>Stream Type - CL</Description>
			<DisplayName>CL</DisplayName>
			<Value>3</Value>
		</EnumEntry>
		<EnumEntry Name="IIDC" NameSpace="Standard">
			<ToolTip>Stream Type - IIDC.</ToolTip>
			<Description>Stream Type - IIDC</Description>
			<DisplayName>IIDC</DisplayName>
			<Value>4</Value>
		</EnumEntry>
		<EnumEntry Name="UVC" NameSpace="Standard">
			<ToolTip>Stream Type - UVC.</ToolTip>
			<Description>Stream Type - UVC</Description>
			<DisplayName>UVC</DisplayName>
			<Value>5</Value>
		</EnumEntry>
		<EnumEntry Name="CXP" NameSpace="Standard">
			<ToolTip>Stream Type - CXP.</ToolTip>
			<Description>Stream Type - CXP</Description>
			<DisplayName>CXP</DisplayName>
			<Value>6</Value>
		</EnumEntry>
		<EnumEntry Name="CLHS" NameSpace="Standard">
			<ToolTip>Stream Type - CLHS.</ToolTip>
			<Description>Stream Type - CLHS</Description>
			<DisplayName>CLHS</DisplayName>
			<Value>7</Value>
		</EnumEntry>
		<EnumEntry Name="U3V" NameSpace="Standard">
			<ToolTip>Stream Type - U3V.</ToolTip>
			<Description>Stream Type - U3V</Description>
			<DisplayName>U3V</DisplayName>
			<Value>8</Value>
		</EnumEntry>
		<EnumEntry Name="ETHERNET" NameSpace="Standard">
			<ToolTip>Stream Type - ETHERNET.</ToolTip>
			<Description>Stream Type - ETHERNET</Description>
			<DisplayName>ETHERNET</DisplayName>
			<Value>9</Value>
		</EnumEntry>
		<EnumEntry Name="PCI" NameSpace="Standard">
			<ToolTip>Stream Type - PCI.</ToolTip>
			<Description>Stream Type - PCI</Description>
			<DisplayName>PCI</DisplayName>
			<Value>10</Value>
		</EnumEntry>
		<Value>8</Value>
	</Enumeration>

	<Integer Name="StreamTotalBufferCount" NameSpace="Standard">
      <ToolTip>Counts the number of image buffers that arrived since stream started.</ToolTip>
      <Description>Counts the number of image buffers that arrived since stream started.</Description>
      <DisplayName>Total Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>StreamTotalBufferCount_RegVal</pValue>
    </Integer>
    <IntReg Name="StreamTotalBufferCount_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x4000</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
	  <PollingTime>1000</PollingTime>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
  </Group>
  <!-- ***************************************** -->
  <!-- BufferHandlingControl -->
  <!-- ***************************************** -->
  <Group Comment="BufferHandlingControl">
    <Integer Name="StreamDefaultBufferCount" NameSpace="Standard">
      <ToolTip>Number of buffers used by default on this stream.</ToolTip>
      <Description>Controls the number of buffers that should be used by default on this stream.</Description>
      <DisplayName>Stream Default Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>StreamDefaultBufferCount_RegVal</pValue>
      <Min>1</Min>
      <pMax>StreamDefaultBufferCountMax_RegVal</pMax>
    </Integer>
    <IntReg Name="StreamDefaultBufferCount_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x1000</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="StreamDefaultBufferCountMax" NameSpace="Standard">
      <ToolTip>Maximum number of buffers used on this stream.</ToolTip>
      <Description>Controls the maximum number of buffers that should be used on this stream. This value is calculated based on the available system memory.</Description>
      <DisplayName>Stream Default Buffer Count Max</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>StreamDefaultBufferCountMax_RegVal</pValue>
      <Min>1</Min>
    </Integer>
    <IntReg Name="StreamDefaultBufferCountMax_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x2000</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
	<Enumeration Name="StreamDefaultBufferCountMode" NameSpace="Standard">
      <!-- Mandatory element -->
      <ToolTip>Controls the number of buffers used for the stream.</ToolTip>
      <Description>Controls the number of buffers used for the stream.</Description>
      <DisplayName>Stream Default Buffer Count Mode</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Manual" NameSpace="Standard">
        <ToolTip>Controls the number of buffers used for the stream.</ToolTip>
        <Description>Controls the number of buffers used for the stream.</Description>
        <DisplayName>Manual</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="Auto" NameSpace="Standard">
        <ToolTip>The number of buffers used for the stream is automatically calculated.</ToolTip>
        <Description>The number of buffers used for the stream is automatically calculated based on the device bandwidth.</Description>
        <DisplayName>Auto</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <pValue>StreamDefaultBufferCountMode_RegVal</pValue>
    </Enumeration>
    <IntReg Name="StreamDefaultBufferCountMode_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x2008</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <!--<Integer Name="StreamAnnouncedBufferCount" NameSpace="Standard">
      <ToolTip>Number of announced (known) buffers on this stream.</ToolTip>
      <Description>Number of announced (known) buffers on this stream. This value is volatile. It may change if additional buffers are announced and/or buffers are revoked by the GenTL Consumer.</Description>
      <DisplayName>Stream Announced Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>-->
    <Enumeration Name="StreamBufferHandlingMode" NameSpace="Standard">
      <!-- Mandatory element -->
      <ToolTip>Available buffer handling modes of this data stream.</ToolTip>
      <Description>Available buffer handling modes of this data stream:</Description>
      <DisplayName>Stream Buffer Handling Mode</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="OldestFirst" NameSpace="Standard">
        <ToolTip>The application always gets the buffer from the head of the output buffer queue (thus, the oldest available one).</ToolTip>
        <Description>The application always gets the buffer from the head of the output buffer queue (thus, the oldest available one). If the output buffer queue is empty, the application waits for a newly acquired buffer until the timeout expires.</Description>
        <DisplayName>Oldest First</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="OldestFirstOverwrite" NameSpace="Standard">
        <ToolTip>The application always gets the buffer from the head of the output buffer queue (thus, the oldest available one).</ToolTip>
        <Description>The application always gets the buffer from the head of the output buffer queue (thus, the oldest available one). If the output buffer queue is empty, the application waits for a newly acquired buffer until the timeout expires. If a new buffer arrives it will overwrite the existing buffer from the head of the queue (behaves like a circular buffer).</Description>
        <DisplayName>Oldest First Overwrite</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="NewestFirst" NameSpace="Standard">
        <ToolTip>The application always gets the buffer from the tail of the output buffer queue (thus, the newest available one).</ToolTip>
        <Description>The application always gets the buffer from the tail of the output buffer queue (thus, the newest available one). If the output buffer queue is empty, the application waits for a newly acquired buffer until the timeout expires.</Description>
        <DisplayName>Newest First</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <EnumEntry Name="NewestFirstOverwrite" NameSpace="Standard">
        <ToolTip>DEPRECATED.  The application always gets the buffer from the tail of the output buffer queue (thus, the newest available one).</ToolTip>
        <Description>DEPRECATED.  This is replaced by NewestOnly.</Description>
        <DisplayName>Newest First Overwrite</DisplayName>
        <Value>3</Value>
      </EnumEntry>
      <EnumEntry Name="NewestOnly" NameSpace="Standard">
        <ToolTip>The application always gets the buffer from the tail of the output buffer queue (thus, the newest available one).</ToolTip>
        <Description>The application always gets the latest completed buffer (the newest one).  If the Output Buffer Queue is empty, the application waits for a newly acquired buffer until the timeout expires.  This buffer handling mode is typically used in a live display GUI where it is important that there is no lag between camera and display.</Description>
        <DisplayName>Newest Only</DisplayName>
        <Value>4</Value>
      </EnumEntry>
      <!--<Value>0</Value>-->
      <pValue>StreamBufferHandlingMode_RegVal</pValue>
    </Enumeration>
    <IntReg Name="StreamBufferHandlingMode_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x1008</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <!--<Integer Name="StreamAnnounceBufferMinimum" NameSpace="Standard">
      <ToolTip>Minimal number of buffers to announce to enable selected buffer handling mode.</ToolTip>
      <Description>Minimal number of buffers to announce to enable selected buffer handling mode.</Description>
      <DisplayName>Stream Announce Buffer Minimum</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamDeliveredFrameCount" NameSpace="Standard">
      <ToolTip>Number of delivered frames since last acquisition start.</ToolTip>
      <Description>Number of delivered frames since last acquisition start.</Description>
      <DisplayName>Stream Delivered Frame Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamLostFrameCount" NameSpace="Standard">
      <ToolTip>Number of lost frames due to queue underrun.</ToolTip>
      <Description>Number of lost frames due to queue underrun. This number is initialized with zero at the time the stream is opened and incremented every time the data could not be acquired because there was no buffer in the input buffer pool.</Description>
      <DisplayName>Stream Lost Frame Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamInputBufferCount" NameSpace="Standard">
      <ToolTip>Number of buffers in the input buffer pool.</ToolTip>
      <Description>Number of buffers in the input buffer pool.</Description>
      <DisplayName>Stream Input Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamOutputBufferCount" NameSpace="Standard">
      <ToolTip>Number of buffers in the output buffer queue.</ToolTip>
      <Description>Number of buffers in the output buffer queue.</Description>
      <DisplayName>Stream Output Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamStartedFrameCount" NameSpace="Standard">
      <ToolTip>Number of frames started in the acquisition engine.</ToolTip>
      <Description>Number of frames started in the acquisition engine. This number is incremented every time a new buffer is started to be filled (data written to) regardless if the buffer is later delivered to the user or discarded for any reason. This number is initialized with 0 at at the time of the stream is opened. It is not reset until the stream is closed.</Description>
      <DisplayName>Stream Started Frame Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="PayloadSize" NameSpace="Standard">
      <ToolTip>Size of the expected data in bytes.</ToolTip>
      <Description>Size of the expected data in bytes. Note that this feature "overwrites" the PayloadSize of the remote device, see also sections "Data Payload Delivery" and "Allocate Memory" of the GenICam GenTL standard.</Description>
      <DisplayName>Payload Size</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
      <Unit>Byte</Unit>
    </Integer>
    <Boolean Name="StreamIsGrabbing" NameSpace="Standard">
      <ToolTip>Flag indicating whether the acquisition engine is started or not.</ToolTip>
      <Description>Flag indicating whether the acquisition engine is started or not. This is independent from the acquisition status of the remote device.</Description>
      <DisplayName>Stream Is Grabbing</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Boolean>
    <Integer Name="StreamChunkCountMaximum" NameSpace="Standard">
      <ToolTip>Maximum number of chunks to be expected in a buffer (can be used to allocate the array for the DSGetBufferChunkData function).</ToolTip>
      <Description>Maximum number of chunks to be expected in a buffer (can be used to allocate the array for the DSGetBufferChunkData function).</Description>
      <DisplayName>Stream Chunk Count Maximum</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="StreamBufferAlignment" NameSpace="Standard">
      <ToolTip>Alignment size in bytes of the buffer passed to DSAnnounceBuffer.</ToolTip>
      <Description>Alignment size in bytes of the buffer passed to DSAnnounceBuffer.</Description>
      <DisplayName>Stream Buffer Alignment</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
      <Unit>Byte</Unit>
    </Integer>-->
	<Boolean Name="StreamCRCCheckEnable" NameSpace="Standard">
      <ToolTip>Enables or disables CRC checks on received images.</ToolTip>
      <Description>Enables or disables CRC checks on received images.</Description>
      <DisplayName>CRC Check Enable</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>StreamCRCCheckEnable_RegVal</pValue>
    </Boolean>
    <IntReg Name="StreamCRCCheckEnable_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x1010</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>

    <Integer Name="StreamBlockTransferSize" NameSpace="Standard">
      <ToolTip>Maximum block size transferred on this stream</ToolTip>
      <Description>Controls the image breakup size that should be used on this stream.</Description>
      <DisplayName>Stream Block Transfer Size</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>StreamBlockTransferSize_RegVal</pValue>
      <Min>1024</Min>
	  <Unit>Byte</Unit>
    </Integer>
    <IntReg Name="StreamBlockTransferSize_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x4018</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
  </Group>
  <Group Comment="StreamDiagnostics">
    <Integer Name="StreamFailedBufferCount" NameSpace="Standard">
      <ToolTip>Number of corrupt images on this stream.</ToolTip>
      <Description>Displays number of corrupt images on this stream.</Description>
      <DisplayName>Failed Buffer Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>StreamFailedBufferCount_RegVal</pValue>
    </Integer>
    <IntReg Name="StreamFailedBufferCount_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x4008</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <PollingTime>1000</PollingTime>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="StreamBufferUnderrunCount" NameSpace="Standard">
      <ToolTip>Number of dropped images on this stream.</ToolTip>
      <Description>Displays number of dropped images on this stream.</Description>
      <DisplayName>Buffer Underrun Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>StreamBufferUnderrunCount_RegVal</pValue>
    </Integer>
    <IntReg Name="StreamBufferUnderrunCount_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x4010</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <PollingTime>1000</PollingTime>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
  </Group>
</RegisterDescription>
