# property configurator test file

log4cpp.rootCategory=ERROR, rootAppender
log4cpp.category.SpinnakerNETCategory=ERROR, SpinnakerNETCategory

log4cpp.appender.rootAppender=ConsoleAppender
log4cpp.appender.rootAppender.layout=PatternLayout
log4cpp.appender.rootAppender.layout.ConversionPattern=[%p] %d [%t] %m%n 

log4cpp.appender.SpinnakerNETCategory=RollingFileAppender
log4cpp.appender.SpinnakerNETCategory.fileName=$(ALLUSERSPROFILE)\Spinnaker\Logs\SpinnakerNET.log
log4cpp.appender.SpinnakerNETCategory.append=true
log4cpp.appender.SpinnakerNETCategory.maxFileSize=1000000
log4cpp.appender.SpinnakerNETCategory.maxBackupIndex=5
log4cpp.appender.SpinnakerNETCategory.layout=PatternLayout
log4cpp.appender.SpinnakerNETCategory.layout.ConversionPattern=[%p] %d [%t] %m%n 