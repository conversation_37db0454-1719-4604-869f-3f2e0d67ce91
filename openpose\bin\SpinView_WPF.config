﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  <log4net>
    <appender name="SpinView.SpinView_WPF" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="%envFolderPath{CommonApplicationData}\\Spinnaker\\Logs\\spinview.txt"/>
      <appendToFile value="True"/>
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="5" />
      <maximumFileSize value="3000KB" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <staticLogFileName value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="[%level]%X{tab}%date%X{tab}%logger%X{tab}%message%newline%exception"/>
      </layout>
    </appender>
    <root>
      <level value="DEBUG"/>
      <appender-ref ref="SpinView.SpinView_WPF"/>
    </root>
  </log4net>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
  </startup>
  <runtime>
    <legacyCorruptedStateExceptionsPolicy enabled="true" />
  </runtime>
</configuration>