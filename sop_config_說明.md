# SOP 配置檔案說明

## 📋 sop_config.json 欄位詳細說明

### 🔧 **sop_steps** (SOP 步驟列表)
包含所有裝配步驟的陣列，每個步驟包含以下欄位：

#### 📊 **各欄位說明**

| 欄位名稱 | 英文原文 | 中文說明 | 範例值 | 備註 |
|---------|---------|---------|--------|------|
| **id** | id | 步驟編號 | 0, 1, 2... | 從 0 開始計算 |
| **name** | name | 步驟名稱 | "準備工作區" | 顯示在監控畫面上 |
| **description** | description | 步驟詳細說明 | "清理工作台，準備所需工具" | 詳細的操作說明 |
| **expected_actions** | expected_actions | 預期動作列表 | ["reaching", "grasping"] | 該步驟應該執行的動作 |
| **duration_estimate** | duration_estimate | 預估完成時間 | 30, 60, 120 | 單位：秒 |

#### 🎯 **expected_actions 動作類型**

| 英文動作名稱 | 中文名稱 | 說明 | 適用場景 |
|-------------|---------|------|---------|
| **reaching** | 伸手動作 | 伸手去拿取工具或零件 | 準備階段、檢查階段 |
| **grasping** | 抓取動作 | 用手抓住、握住物品 | 拿取零件、操作工具 |
| **screwing** | 螺絲動作 | 使用螺絲刀轉動螺絲 | 固定零件、鎖緊螺絲 |
| **rotating** | 旋轉動作 | 旋轉零件或工具 | 安裝零件、調整位置 |

---

### ⚙️ **action_types** (動作類型設定)
定義每種動作的檢測參數：

#### 🎚️ **threshold 閾值說明**

| 動作類型 | 英文名稱 | 中文名稱 | 閾值 | 信心度要求 | 說明 |
|---------|---------|---------|------|-----------|------|
| **reaching** | reaching | 伸手動作 | 0.5 | 50% | 動作簡單，要求較低 |
| **grasping** | grasping | 抓取動作 | 0.8 | 80% | 需要精確檢測 |
| **screwing** | screwing | 螺絲動作 | 0.7 | 70% | 重要動作，中等要求 |
| **rotating** | rotating | 旋轉動作 | 0.6 | 60% | 中等重要性 |

---

## 📝 **實際範例解讀**

### 步驟 0：準備工作區
```json
{
  "id": 0,                                    // 第一個步驟 (編號從0開始)
  "name": "準備工作區",                        // 步驟名稱
  "description": "清理工作台，準備所需工具",    // 詳細說明
  "expected_actions": ["reaching", "grasping"], // 預期會有伸手和抓取動作
  "duration_estimate": 30                     // 預估30秒完成
}
```

### 步驟 3：鎖緊螺絲
```json
{
  "id": 3,                                    // 第四個步驟
  "name": "鎖緊螺絲",                         // 步驟名稱
  "description": "使用螺絲刀鎖緊所有螺絲",     // 詳細說明
  "expected_actions": ["screwing"],           // 只預期螺絲動作
  "duration_estimate": 90                     // 預估90秒完成
}
```

---

## 🔧 **如何修改配置**

### 1. **新增步驟**
在 `sop_steps` 陣列中新增一個物件：
```json
{
  "id": 7,                                    // 新的步驟編號
  "name": "您的新步驟",                       // 自訂步驟名稱
  "description": "詳細的操作說明",             // 自訂說明
  "expected_actions": ["reaching", "grasping"], // 選擇適合的動作
  "duration_estimate": 60                     // 預估時間
}
```

### 2. **修改動作閾值**
在 `action_types` 中調整 `threshold` 值：
- **降低閾值** (如 0.7 → 0.5)：更容易檢測到動作，但可能誤判
- **提高閾值** (如 0.5 → 0.8)：更嚴格檢測，但可能漏檢

### 3. **修改預估時間**
調整 `duration_estimate` 值：
- 30 = 30秒
- 60 = 1分鐘
- 120 = 2分鐘
- 300 = 5分鐘

---

## ⚠️ **注意事項**

1. **JSON 格式**：不能使用 `#` 註解，必須是有效的 JSON 格式
2. **步驟編號**：`id` 必須從 0 開始，依序遞增
3. **動作名稱**：`expected_actions` 中的動作必須在 `action_types` 中有定義
4. **檔案編碼**：建議使用 UTF-8 編碼以正確顯示中文

---

## 🚀 **使用方式**

1. 修改 `sop_config.json` 檔案
2. 在 `main.py` 中指定配置檔案：
   ```python
   monitor = AssemblySOPMonitor(
       sop_config_file="sop_config.json"  # 指定您的配置檔案
   )
   ```
3. 執行程式開始監控
