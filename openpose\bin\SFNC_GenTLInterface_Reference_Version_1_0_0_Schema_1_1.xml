<?xml version="1.0" encoding="UTF-8"?>
<RegisterDescription xmlns="http://www.genicam.org/GenApi/Version_1_1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.genicam.org/GenApi/Version_1_1 http://www.genicam.org/GenApi/GenApiSchema_Version_1_1.xsd" ModelName="SFNC_Camera" VendorName="Generic" ToolTip="Reference SFNC camera XML (autogenerated)" StandardNameSpace="None" SchemaMajorVersion="1" SchemaMinorVersion="1" SchemaSubMinorVersion="0" MajorVersion="1" MinorVersion="0" SubMinorVersion="0" ProductGuid="11111111-**************-************" VersionGuid="AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE">

  <Category Name="Root" NameSpace="Standard">
    <Visibility>Beginner</Visibility>
    <pFeature>InterfaceInformation</pFeature>
    <pFeature>DeviceEnumeration</pFeature>
  </Category>
  <Port Name="Device" NameSpace="Standard">
  </Port>
  <Category Name="InterfaceInformation" NameSpace="Standard">
    <ToolTip>Category that contains all Interface Information features of the Interface module.</ToolTip>
    <Description>Category that contains all Interface Information features of the Interface module.</Description>
    <DisplayName>Interface Information</DisplayName>
    <Visibility>Beginner</Visibility>
    <pFeature>InterfaceID</pFeature>
    <pFeature>InterfaceDisplayName</pFeature>
    <pFeature>InterfaceType</pFeature>
    <pFeature>GevInterfaceGatewaySelector</pFeature>
    <pFeature>GevInterfaceGateway</pFeature>
    <pFeature>GevInterfaceMACAddress</pFeature>
    <pFeature>GevInterfaceSubnetSelector</pFeature>
    <pFeature>GevInterfaceSubnetIPAddress</pFeature>
    <pFeature>GevInterfaceSubnetMask</pFeature>
  </Category>
  <Category Name="DeviceEnumeration" NameSpace="Standard">
    <ToolTip>Category that contains all Device Enumeration features of the Interface module.</ToolTip>
    <Description>Category that contains all Device Enumeration features of the Interface module.</Description>
    <DisplayName>Device Enumeration</DisplayName>
    <Visibility>Expert</Visibility>
    <pFeature>DeviceUpdateList</pFeature>
    <pFeature>DeviceSelector</pFeature>
    <pFeature>DeviceID</pFeature>
    <pFeature>DeviceVendorName</pFeature>
    <pFeature>DeviceModelName</pFeature>
    <pFeature>DeviceAccessStatus</pFeature>
    <pFeature>DeviceSerialNumber</pFeature>
    <pFeature>DeviceUserID</pFeature>
    <pFeature>GevDeviceIPAddress</pFeature>
    <pFeature>GevDeviceSubnetMask</pFeature>
    <pFeature>GevDeviceMACAddress</pFeature>
    <pFeature>AutoForceIP</pFeature>
  </Category> 
  <String Name="InterfaceID" NameSpace="Standard">
    <ToolTip>Transport layer Producer wide unique identifier of the selected interface.</ToolTip>
    <Description>Transport layer Producer wide unique identifier of the selected interface.</Description>
    <DisplayName>Interface ID  </DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <String Name="InterfaceDisplayName" NameSpace="Standard">
    <ToolTip>User readable name of the selected interface.</ToolTip>
    <Description>User readable name of the selected interface.</Description>
    <DisplayName>Interface Display Name</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <Enumeration Name="InterfaceType" NameSpace="Standard">
    <ToolTip>Transport layer type of the interface.</ToolTip>
    <Description>Transport layer type of the interface.</Description>
    <DisplayName>Interface Type</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <EnumEntry Name="GigEVision" NameSpace="Standard">
      <ToolTip>GigE Vision.</ToolTip>
      <Description>GigE Vision</Description>
      <DisplayName>Gig E Vision</DisplayName>
      <Value>0</Value>
    </EnumEntry>
    <EnumEntry Name="CameraLink" NameSpace="Standard">
      <ToolTip>Camera Link.</ToolTip>
      <Description>Camera Link</Description>
      <DisplayName>Camera Link</DisplayName>
      <Value>1</Value>
    </EnumEntry>
    <EnumEntry Name="CameraLinkHS" NameSpace="Standard">
      <ToolTip>Camera Link High Speed.</ToolTip>
      <Description>Camera Link High Speed</Description>
      <DisplayName>Camera Link HS</DisplayName>
      <Value>2</Value>
    </EnumEntry>
    <EnumEntry Name="CoaXPress" NameSpace="Standard">
      <ToolTip>CoaXPress.</ToolTip>
      <Description>CoaXPress</Description>
      <DisplayName>Coa X Press</DisplayName>
      <Value>3</Value>
    </EnumEntry>
    <EnumEntry Name="USB3Vision" NameSpace="Standard">
      <ToolTip>USB3 Vision.</ToolTip>
      <Description>USB3 Vision</Description>
      <DisplayName>USB 3 Vision</DisplayName>
      <Value>4</Value>
    </EnumEntry>
    <EnumEntry Name="Custom" NameSpace="Standard">
      <ToolTip>Custom transport layer.</ToolTip>
      <Description>Custom transport layer</Description>
      <DisplayName>Custom</DisplayName>
      <Value>5</Value>
    </EnumEntry>
    <Value>0</Value>
  </Enumeration>
  <Integer Name="GevInterfaceGatewaySelector" NameSpace="Standard">
    <ToolTip>Selector for the different gateway entries for this interface.</ToolTip>
    <Description>Selector for the different gateway entries for this interface. The selector is 0-based.</Description>
    <DisplayName>Gev Interface Gateway Selector</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <Value>0</Value>
    <pSelected>GevInterfaceGateway</pSelected>
  </Integer>
  <Integer Name="GevInterfaceGateway" NameSpace="Standard">
    <ToolTip>IP address of the selected gateway entry of this interface.</ToolTip>
    <Description>IP address of the selected gateway entry of this interface.</Description>
    <DisplayName>Gev Interface Gateway</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Integer Name="GevInterfaceMACAddress" NameSpace="Standard">
    <ToolTip>48-bit MAC address of this interface.</ToolTip>
    <Description>48-bit MAC address of this interface.</Description>
    <DisplayName>Gev Interface MAC Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Integer Name="GevInterfaceSubnetSelector" NameSpace="Standard">
    <ToolTip>Selector for the subnet of this interface.</ToolTip>
    <Description>Selector for the subnet of this interface.</Description>
    <DisplayName>Gev Interface Subnet Selector</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <Value>0</Value>
    <pSelected>GevInterfaceSubnetIPAddress</pSelected>
    <pSelected>GevInterfaceSubnetMask</pSelected>
  </Integer>
  <Integer Name="GevInterfaceSubnetIPAddress" NameSpace="Standard">
    <ToolTip>IP address of the selected subnet of this interface.</ToolTip>
    <Description>IP address of the selected subnet of this interface.</Description>
    <DisplayName>Gev Interface Subnet IP Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Integer Name="GevInterfaceSubnetMask" NameSpace="Standard">
    <ToolTip>Subnet mask of the selected subnet of this interface.</ToolTip>
    <Description>Subnet mask of the selected subnet of this interface.</Description>
    <DisplayName>Gev Interface Subnet Mask</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Command Name="DeviceUpdateList" NameSpace="Standard">
    <ToolTip>Updates the internal device list.</ToolTip>
    <Description>Updates the internal device list.</Description>
    <DisplayName>Device Update List</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <Value>0</Value>
    <CommandValue>0</CommandValue>
  </Command>
  <Integer Name="DeviceSelector" NameSpace="Standard">
    <ToolTip>Selector for the different devices on this interface.</ToolTip>
    <Description>Selector for the different devices on this interface. This value only changes on execution of "DeviceUpdateList". The selector is 0-based in order to match the index of the C interface.</Description>
    <DisplayName>Device Selector</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <Value>0</Value>
    <pSelected>DeviceID</pSelected>
    <pSelected>DeviceVendorName</pSelected>
    <pSelected>DeviceModelName</pSelected>
    <pSelected>DeviceAccessStatus</pSelected>
    <pSelected>DeviceSerialNumber</pSelected>
    <pSelected>DeviceUserID</pSelected>
    <pSelected>GevDeviceIPAddress</pSelected>
    <pSelected>GevDeviceSubnetMask</pSelected>
    <pSelected>GevDeviceMACAddress</pSelected>
  </Integer>
  <String Name="DeviceID" NameSpace="Standard">
    <ToolTip>Interface wide unique identifier of the selected device.</ToolTip>
    <Description>Interface wide unique identifier of the selected device. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device ID</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <String Name="DeviceVendorName" NameSpace="Standard">
    <ToolTip>Name of the device vendor.</ToolTip>
    <Description>Name of the device vendor. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Vendor Name</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <String Name="DeviceModelName" NameSpace="Standard">
    <ToolTip>Name of the device model.</ToolTip>
    <Description>Name of the device model. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Model Name</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <Enumeration Name="DeviceAccessStatus" NameSpace="Standard">
    <ToolTip>Gives the device's access status at the moment of the last execution of "DeviceUpdateList".</ToolTip>
    <Description>Gives the device's access status at the moment of the last execution of "DeviceUpdateList". This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Access Status</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <EnumEntry Name="ReadWrite" NameSpace="Standard">
      <ToolTip>Full access.</ToolTip>
      <Description>Full access</Description>
      <DisplayName>Read Write</DisplayName>
      <Value>0</Value>
    </EnumEntry>
    <EnumEntry Name="ReadOnly" NameSpace="Standard">
      <ToolTip>Read-only access.</ToolTip>
      <Description>Read-only access</Description>
      <DisplayName>Read Only</DisplayName>
      <Value>1</Value>
    </EnumEntry>
    <EnumEntry Name="NoAccess" NameSpace="Standard">
      <ToolTip>Another device has exclusive access.</ToolTip>
      <Description>Another device has exclusive access</Description>
      <DisplayName>No Access</DisplayName>
      <Value>2</Value>
    </EnumEntry>
    <Value>0</Value>
  </Enumeration>
  <String Name="DeviceSerialNumber" NameSpace="Standard">
    <ToolTip>Serial number of the remote device.</ToolTip>
    <Description>Serial number of the remote device. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Serial Number</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <String Name="DeviceUserID" NameSpace="Standard">
    <ToolTip>User-programmable device identifier of the remote device.</ToolTip>
    <Description>User-programmable device identifier of the remote device. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device User ID</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>abc</Value>
  </String>
  <Integer Name="GevDeviceIPAddress" NameSpace="Standard">
    <ToolTip>Current IP address of the GVCP interface of the selected remote device.</ToolTip>
    <Description>Current IP address of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device IP Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Integer Name="GevDeviceSubnetMask" NameSpace="Standard">
    <ToolTip>Current subnet mask of the GVCP interface of the selected remote device.</ToolTip>
    <Description>Current subnet mask of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device Subnet Mask</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Integer Name="GevDeviceMACAddress" NameSpace="Standard">
    <ToolTip>48-bit MAC address of the GVCP interface of the selected remote device.</ToolTip>
    <Description>48-bit MAC address of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device MAC Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <Value>0</Value>
  </Integer>
  <Command Name="AutoForceIP" NameSpace="Standard">
    <ToolTip>Automatically Forces Cameras on interface to an ipAddress on the same subnet as the interface.</ToolTip>
    <Description>Automatically Forces Cameras on interface to an ipAddress on the same subnet as the interface.</Description>
    <DisplayName>Auto Force IP</DisplayName>
    <Visibility>Expert</Visibility>
    <pValue>AutoForceIP_CtrlValueReg</pValue>
    <CommandValue>1</CommandValue>
  </Command>
  <IntReg Name="AutoForceIP_CtrlValueReg">
    <Visibility>Invisible</Visibility>
    <Address>0x4000</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign> 
    <Endianess>LittleEndian</Endianess>
  </IntReg>
</RegisterDescription>