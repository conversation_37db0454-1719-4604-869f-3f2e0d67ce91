#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試 OpenPose
"""

import sys
import os
from pathlib import Path
import cv2
import numpy as np

def setup_openpose_path():
    """設置 OpenPose 路徑"""
    # OpenPose 資料夾路徑
    openpose_dir = Path.cwd() / "openpose"
    
    # Python 模組路徑
    python_paths = [
        openpose_dir / "bin" / "python" / "openpose" / "Release",
        openpose_dir / "bin" / "python" / "openpose" / "Debug",
        openpose_dir / "python",
    ]
    
    for path in python_paths:
        if path.exists():
            print(f"📍 找到 Python 模組路徑: {path}")
            if str(path) not in sys.path:
                sys.path.insert(0, str(path))
                print(f"✅ 已添加到 Python 路徑")
            return path
    
    print("❌ 未找到 Python 模組路徑")
    return None

def test_openpose_import():
    """測試 OpenPose 導入"""
    print("🧪 測試 OpenPose 導入...")
    
    try:
        # 嘗試導入 OpenPose
        import pyopenpose as op
        print("✅ OpenPose 導入成功！")
        return op
    except ImportError as e:
        print(f"❌ OpenPose 導入失敗: {e}")
        print("💡 嘗試其他導入方式...")
        
        try:
            # 嘗試其他導入方式
            sys.path.append('openpose/bin/python/openpose/Release')
            sys.path.append('openpose/bin/python/openpose/Debug')
            import pyopenpose as op
            print("✅ OpenPose 導入成功（第二次嘗試）！")
            return op
        except ImportError as e2:
            print(f"❌ 第二次導入也失敗: {e2}")
            return None

def initialize_openpose(op):
    """初始化 OpenPose"""
    print("⚙️ 初始化 OpenPose...")
    
    try:
        # 設置參數
        params = dict()
        params["model_folder"] = "openpose/models/"
        params["face"] = False
        params["hand"] = True  # 啟用手部檢測
        params["body"] = 1     # 啟用身體檢測
        params["net_resolution"] = "368x368"  # 降低解析度提高速度
        
        # 初始化 OpenPose
        opWrapper = op.WrapperPython()
        opWrapper.configure(params)
        opWrapper.start()
        
        print("✅ OpenPose 初始化成功！")
        return opWrapper
        
    except Exception as e:
        print(f"❌ OpenPose 初始化失敗: {e}")
        return None

def test_video_processing(opWrapper):
    """測試影片處理"""
    print("🎬 測試影片處理...")
    
    try:
        # 開啟影片
        cap = cv2.VideoCapture("1.mp4")
        if not cap.isOpened():
            print("❌ 無法開啟影片檔案")
            return False
        
        print("📹 影片開啟成功，開始處理...")
        
        frame_count = 0
        detection_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame_count += 1
            
            # 每10幀處理一次（提高速度）
            if frame_count % 10 != 0:
                continue
            
            # OpenPose 處理
            datum = op.Datum()
            datum.cvInputData = frame
            opWrapper.emplaceAndPop(op.VectorDatum([datum]))
            
            # 檢查是否檢測到人
            if datum.poseKeypoints is not None and len(datum.poseKeypoints) > 0:
                detection_count += 1
                print(f"🎯 第 {frame_count} 幀: 檢測到 {len(datum.poseKeypoints)} 個人")
                
                # 檢查手部關鍵點
                if datum.handKeypoints is not None and len(datum.handKeypoints) > 0:
                    print(f"👋 檢測到手部關鍵點")
            
            # 顯示結果
            result_frame = datum.cvOutputData
            if result_frame is not None:
                cv2.imshow('OpenPose Test', result_frame)
            else:
                cv2.imshow('OpenPose Test', frame)
            
            # 按 'q' 退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
            
            # 處理前100幀就夠了
            if frame_count >= 100:
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"📊 處理結果:")
        print(f"   總幀數: {frame_count}")
        print(f"   檢測到人的幀數: {detection_count}")
        print(f"   檢測率: {detection_count/max(frame_count//10, 1)*100:.1f}%")
        
        return detection_count > 0
        
    except Exception as e:
        print(f"❌ 影片處理失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 OpenPose 直接測試開始")
    print("=" * 50)
    
    # 1. 設置路徑
    python_path = setup_openpose_path()
    if not python_path:
        return False
    
    # 2. 測試導入
    op = test_openpose_import()
    if not op:
        return False
    
    # 3. 初始化
    opWrapper = initialize_openpose(op)
    if not opWrapper:
        return False
    
    # 4. 測試影片處理
    success = test_video_processing(opWrapper)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 OpenPose 測試成功！")
        print("✅ 可以開始真正的螺絲檢測了！")
    else:
        print("❌ OpenPose 測試失敗")
        print("💡 請檢查安裝或使用模擬模式")
    
    return success

if __name__ == "__main__":
    main()
