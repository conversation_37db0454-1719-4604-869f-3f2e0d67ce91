<?xml version="1.0" encoding="UTF-8"?>
<RegisterDescription xmlns="http://www.genicam.org/GenApi/Version_1_1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.genicam.org/GenApi/Version_1_1 http://www.genicam.org/GenApi/GenApiSchema_Version_1_1.xsd" ModelName="SFNC_Camera" VendorName="Generic" ToolTip="Reference SFNC camera XML (autogenerated)" StandardNameSpace="None" SchemaMajorVersion="1" SchemaMinorVersion="1" SchemaSubMinorVersion="0" MajorVersion="1" MinorVersion="0" SubMinorVersion="0" ProductGuid="11111111-**************-************" VersionGuid="AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE">

  <Category Name="Root" NameSpace="Standard">
    <Visibility>Beginner</Visibility>
    <pFeature>InterfaceInformation</pFeature>
    <pFeature>DeviceEnumeration</pFeature>
  </Category>
  <Port Name="Device" NameSpace="Standard">
  </Port>
  <Category Name="InterfaceInformation" NameSpace="Standard">
    <ToolTip>Category that contains all Interface Information features of the Interface module.</ToolTip>
    <Description>Category that contains all Interface Information features of the Interface module.</Description>
    <DisplayName>Interface Information</DisplayName>
    <Visibility>Beginner</Visibility>
    <pFeature>InterfaceID</pFeature>
    <pFeature>InterfaceDisplayName</pFeature>
    <pFeature>InterfaceType</pFeature>
    <pFeature>GevInterfaceGateway</pFeature>
    <pFeature>GevInterfaceMACAddress</pFeature>
    <pFeature>GevInterfaceIPAddress</pFeature>
    <pFeature>GevInterfaceSubnetMask</pFeature>
    <pFeature>POEStatus</pFeature>
    <pFeature>GevActionDeviceKey</pFeature>
    <pFeature>GevActionGroupKey</pFeature>
    <pFeature>GevActionGroupMask</pFeature>
    <pFeature>GevActionTime</pFeature>
    <pFeature>ActionCommand</pFeature>
    <pFeature>DeviceUnlock</pFeature>
  </Category>
  <Category Name="DeviceEnumeration" NameSpace="Standard">
    <ToolTip>Category that contains all Device Enumeration features of the Interface module.</ToolTip>
    <Description>Category that contains all Device Enumeration features of the Interface module.</Description>
    <DisplayName>Device Enumeration</DisplayName>
    <Visibility>Expert</Visibility>
    <pFeature>DeviceUpdateList</pFeature>
    <pFeature>DeviceCount</pFeature>
    <pFeature>DeviceSelector</pFeature>
    <pFeature>DeviceID</pFeature>
    <pFeature>DeviceVendorName</pFeature>
    <pFeature>DeviceModelName</pFeature>
    <pFeature>DeviceAccessStatus</pFeature>
    <pFeature>GevDeviceIPAddress</pFeature>
    <pFeature>GevDeviceSubnetMask</pFeature>
    <pFeature>GevDeviceMACAddress</pFeature>
    <pFeature>AutoForceIP</pFeature>
    <pFeature>IncompatibleDeviceCount</pFeature>
    <pFeature>IncompatibleDeviceSelector</pFeature>
    <pFeature>IncompatibleDeviceID</pFeature>
    <pFeature>IncompatibleDeviceVendorName</pFeature>
    <pFeature>IncompatibleDeviceModelName</pFeature>
  </Category>
  <StringReg Name="InterfaceID" NameSpace="Standard">
    <ToolTip>Transport layer Producer wide unique identifier of the selected interface.</ToolTip>
    <Description>Transport layer Producer wide unique identifier of the selected interface.</Description>
    <DisplayName>Interface ID  </DisplayName>
    <Visibility>Expert</Visibility>
    <Address>0x0000</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="InterfaceDisplayName" NameSpace="Standard">
    <ToolTip>User readable name of the selected interface.</ToolTip>
    <Description>User readable name of the selected interface.</Description>
    <DisplayName>Interface Display Name</DisplayName>
    <Visibility>Expert</Visibility>
    <Address>0x00FA</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="InterfaceType" NameSpace="Standard">
    <ToolTip>Transport layer type of the interface.</ToolTip>
    <Description>Transport layer type of the interface.</Description>
    <DisplayName>Interface Type</DisplayName>
    <Visibility>Expert</Visibility>
    <Address>0x01F4</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <Integer Name="GevInterfaceGateway" NameSpace="Standard">
    <ToolTip>IP address of the selected gateway entry of this interface.</ToolTip>
    <Description>IP address of the selected gateway entry of this interface.</Description>
    <DisplayName>Gev Interface Gateway</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevInterfaceGateway_RegVal</pValue>
  </Integer>
  <IntReg Name="GevInterfaceGateway_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x4018</Address>
    <Length>4</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevInterfaceMACAddress" NameSpace="Standard">
    <ToolTip>48-bit MAC address of this interface.</ToolTip>
    <Description>48-bit MAC address of this interface.</Description>
    <DisplayName>Gev Interface MAC Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevInterfaceMACAddress_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevInterfaceMACAddress_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x4020</Address>
    <Length>8</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevInterfaceIPAddress" NameSpace="Standard">
    <ToolTip>IP address of the selected subnet of this interface.</ToolTip>
    <Description>IP address of the selected subnet of this interface.</Description>
    <DisplayName>Gev Interface IP Address</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevInterfaceIPAddress_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevInterfaceIPAddress_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x4008</Address>
    <Length>4</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevInterfaceSubnetMask" NameSpace="Standard">
    <ToolTip>Subnet mask of the selected subnet of this interface.</ToolTip>
    <Description>Subnet mask of the selected subnet of this interface.</Description>
    <DisplayName>Gev Interface Subnet Mask</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevInterfaceSubnetMask_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevInterfaceSubnetMask_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x4010</Address>
    <Length>4</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Enumeration Name="POEStatus" NameSpace="Standard">
    <ToolTip>Reports and controls the interface's power over Ethernet status.</ToolTip>
    <Description>Reports and controls the interface's power over Ethernet status.</Description>
    <DisplayName>POE Status</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>POEStatusAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <EnumEntry Name="NotSupported" NameSpace="Standard">
      <ToolTip>Not Supported.</ToolTip>
      <Description>Not Supported</Description>
      <DisplayName>Not Supported</DisplayName>
      <Value>-1</Value>
    </EnumEntry>
    <EnumEntry Name="PowerOff" NameSpace="Standard">
      <ToolTip>Power is Off.</ToolTip>
      <Description>Power is Off</Description>
      <DisplayName>Power Off</DisplayName>
      <Value>0</Value>
    </EnumEntry>
    <EnumEntry Name="PowerOn" NameSpace="Standard">
      <ToolTip>Power is On.</ToolTip>
      <Description>Power is On</Description>
      <DisplayName>Power On</DisplayName>
      <Value>1</Value>
    </EnumEntry>
    <pValue>POEStatus_RegVal</pValue>
  </Enumeration>
  <IntReg Name="POEStatusAvailable_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x5020</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
  </IntReg>
  <IntReg Name="POEStatus_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x4028</Address>
    <Length>4</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Command Name="DeviceUpdateList" NameSpace="Standard">
    <ToolTip>Updates the internal device list.</ToolTip>
    <Description>Updates the internal device list.</Description>
    <DisplayName>Device Update List</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <Value>0</Value>
    <CommandValue>0</CommandValue>
  </Command>
  <Integer Name="DeviceSelector" NameSpace="Standard">
    <ToolTip>Selector for the different devices on this interface.</ToolTip>
    <Description>Selector for the different devices on this interface. This value only changes on execution of "DeviceUpdateList". The selector is 0-based in order to match the index of the C interface.</Description>
    <DisplayName>Device Selector</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>DeviceSelector_RegVal</pValue>
  </Integer>
  <IntReg Name="DeviceSelectorAvailable_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x1604</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
  </IntReg>
  <IntReg Name="DeviceSelector_RegVal">
    <Visibility>Invisible</Visibility>
    <Address>0x1000</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="IncompatibleDeviceSelector" NameSpace="Standard">
    <ToolTip>Selector for the devices that are not compatible with Spinnaker on this interface.</ToolTip>
    <Description>Selector for the devices that are not compatible with Spinnaker on this interface. This value only changes on execution of "DeviceUpdateList". The selector is 0-based in order to match the index of the C interface.</Description>
    <DisplayName>Incompatible Device Selector</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>IncompatibleDeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>IncompatibleDeviceSelector_RegVal</pValue>
  </Integer>
  <IntReg Name="IncompatibleDeviceSelectorAvailable_RegVal">
      <Visibility>Invisible</Visibility>
      <Address>0x160C</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
  </IntReg>
  <IntReg Name="IncompatibleDeviceSelector_RegVal">
    <Visibility>Invisible</Visibility>
    <Address>0x1400</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
 <Integer Name="DeviceCount" NameSpace="Standard">
    <ToolTip>Number of compatible devices detected on current interface.</ToolTip>
    <Description>Number of compatible devices detected on current interface.</Description>
    <DisplayName>Device Count</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>DeviceCount_RegVal</pValue>
  </Integer>
  <IntReg Name="DeviceCount_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x1614</Address>
    <Length>8</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <StringReg Name="DeviceID" NameSpace="Standard">
    <ToolTip>Interface wide unique identifier of the selected device.</ToolTip>
    <Description>Interface wide unique identifier of the selected device. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device ID</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x1008</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="DeviceVendorName" NameSpace="Standard">
    <ToolTip>Name of the device vendor.</ToolTip>
    <Description>Name of the device vendor. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Vendor Name</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x1102</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="DeviceModelName" NameSpace="Standard">
    <ToolTip>Name of the device model.</ToolTip>
    <Description>Name of the device model. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Model Name</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x11FC</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
   <Integer Name="IncompatibleDeviceCount" NameSpace="Standard">
    <ToolTip>Number of incompatible devices detected on current interface.</ToolTip>
    <Description>Number of incompatible devices detected on current interface.</Description>
    <DisplayName>Incompatible Device Count</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>IncompatibleDeviceCount_RegVal</pValue>
  </Integer>
  <IntReg Name="IncompatibleDeviceCount_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x161C</Address>
    <Length>8</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <StringReg Name="IncompatibleDeviceID" NameSpace="Standard">
    <ToolTip>Interface wide unique identifier of the selected incompatible device.</ToolTip>
    <Description>Interface wide unique identifier of the selected incompatible device. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Incompatible Device ID</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>IncompatibleDeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x1408</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="IncompatibleDeviceVendorName" NameSpace="Standard">
    <ToolTip>Name of the incompatible device vendor.</ToolTip>
    <Description>Name of the incompatible device vendor. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Incompatible Device Vendor Name</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>IncompatibleDeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x1502</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <StringReg Name="IncompatibleDeviceModelName" NameSpace="Standard">
    <ToolTip>Name of the incompatible device model.</ToolTip>
    <Description>Name of the incompatible device model. This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Incompatible Device Model Name</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>IncompatibleDeviceSelectorAvailable_RegVal</pIsAvailable>
    <Address>0x15FC</Address>
    <Length>250</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <Enumeration Name="DeviceAccessStatus" NameSpace="Standard">
    <ToolTip>Gives the device's access status at the moment of the last execution of "DeviceUpdateList".</ToolTip>
    <Description>Gives the device's access status at the moment of the last execution of "DeviceUpdateList". This value only changes on execution of "DeviceUpdateList".</Description>
    <DisplayName>Device Access Status</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RO</ImposedAccessMode>
   <EnumEntry Name="Unknown" NameSpace="Standard">
      <ToolTip>Unknown access.</ToolTip>
      <Description>Unknown access</Description>
      <DisplayName>Unknown</DisplayName>
      <Value>0</Value>
    </EnumEntry>
    <EnumEntry Name="ReadWrite" NameSpace="Standard">
      <ToolTip>Full access.</ToolTip>
      <Description>Full access</Description>
      <DisplayName>Read Write</DisplayName>
      <Value>1</Value>
    </EnumEntry>
    <EnumEntry Name="ReadOnly" NameSpace="Standard">
      <ToolTip>Read-only access.</ToolTip>
      <Description>Read-only access</Description>
      <DisplayName>Read Only</DisplayName>
      <Value>2</Value>
    </EnumEntry>
    <EnumEntry Name="NoAccess" NameSpace="Standard">
      <ToolTip>Another device has exclusive access.</ToolTip>
      <Description>Another device has exclusive access</Description>
      <DisplayName>No Access</DisplayName>
      <Value>3</Value>
    </EnumEntry>
    <pValue>DeviceAccessStatus_RegVal</pValue>
  </Enumeration>
  <IntReg Name="DeviceAccessStatus_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x12F6</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevDeviceIPAddress" NameSpace="Standard">
    <ToolTip>Current IP address of the GVCP interface of the selected remote device.</ToolTip>
    <Description>Current IP address of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device IP Address</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevDeviceIPAddress_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevDeviceIPAddress_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5000</Address>
    <Length>4</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevDeviceSubnetMask" NameSpace="Standard">
    <ToolTip>Current subnet mask of the GVCP interface of the selected remote device.</ToolTip>
    <Description>Current subnet mask of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device Subnet Mask</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevDeviceSubnetMask_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevDeviceSubnetMask_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5008</Address>
    <Length>4</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevDeviceMACAddress" NameSpace="Standard">
    <ToolTip>48-bit MAC address of the GVCP interface of the selected remote device.</ToolTip>
    <Description>48-bit MAC address of the GVCP interface of the selected remote device.</Description>
    <DisplayName>Gev Device MAC Address</DisplayName>
    <Visibility>Expert</Visibility>
    <pIsAvailable>DeviceSelectorAvailable_RegVal</pIsAvailable>
    <ImposedAccessMode>RO</ImposedAccessMode>
    <pValue>GevDeviceMACAddress_RegVal</pValue>
    <Representation>HexNumber</Representation>
  </Integer>
  <IntReg Name="GevDeviceMACAddress_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5018</Address>
    <Length>8</Length>
    <AccessMode>RO</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Command Name="AutoForceIP" NameSpace="Standard">
    <ToolTip>Automatically forces any cameras on interface to an IP Address on the same subnet as the interface.</ToolTip>
    <Description>Automatically forces any cameras on interface to an IP Address on the same subnet as the interface.</Description>
    <DisplayName>Auto Force IP</DisplayName>
    <Visibility>Expert</Visibility>
    <pValue>AutoForceIP_CtrlValueReg</pValue>
    <CommandValue>1</CommandValue>
  </Command>
  <IntReg Name="AutoForceIP_CtrlValueReg">
    <Visibility>Invisible</Visibility>
    <Address>0x4000</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <StringReg Name="DeviceUnlock" NameSpace="Standard">
    <ToolTip>Unlocks devices for internal use.</ToolTip>
    <Description>Unlocks devices for internal use.</Description>
    <DisplayName>Device Unlock</DisplayName>
    <Visibility>Expert</Visibility>
    <Address>0x13F6</Address>
    <Length>256</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
  </StringReg>
  <Integer Name="GevActionDeviceKey" NameSpace="Standard">
    <ToolTip>Authorization key.</ToolTip>
    <Description>Key to authorize the action for the device.</Description>
    <DisplayName>Action Device Key</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>GevActionDeviceKey_RegVal</pValue>
    <Representation>PureNumber</Representation>
  </Integer>
  <IntReg Name="GevActionDeviceKey_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5028</Address>
    <Length>4</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevActionGroupKey" NameSpace="Standard">
    <ToolTip>Provides the key that the device will use to validate the action on reception of the action protocol message.</ToolTip>
    <Description>Provides the key that the device will use to validate the action on reception of the action protocol message.</Description>
    <DisplayName>Action Group Key</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>GevActionGroupKey_RegVal</pValue>
    <Representation>PureNumber</Representation>
  </Integer>
  <IntReg Name="GevActionGroupKey_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x502C</Address>
    <Length>4</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevActionGroupMask" NameSpace="Standard">
    <ToolTip>Provides the mask that the device will use to validate the action on reception of the action protocol message.</ToolTip>
    <Description>Provides the mask that the device will use to validate the action on reception of the action protocol message.</Description>
    <DisplayName>Action Group Mask</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>GevActionGroupMask_RegVal</pValue>
    <Representation>PureNumber</Representation>
  </Integer>
  <IntReg Name="GevActionGroupMask_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5030</Address>
    <Length>4</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Integer Name="GevActionTime" NameSpace="Standard">
    <ToolTip>Provides the time in nanoseconds when the action is to be executed.</ToolTip>
    <Description>Provides the time in nanoseconds when the action is to be executed.</Description>
    <DisplayName>Action Time</DisplayName>
    <Visibility>Expert</Visibility>
    <ImposedAccessMode>RW</ImposedAccessMode>
    <pValue>GevActionTime_RegVal</pValue>
    <Representation>PureNumber</Representation>
  </Integer>
  <IntReg Name="GevActionTime_RegVal" NameSpace="Standard">
    <Visibility>Invisible</Visibility>
    <Address>0x5034</Address>
    <Length>8</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Unsigned</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
  <Command Name="ActionCommand" NameSpace="Standard">
    <ToolTip>Issues an Action Command to attached GEV devices on interface.</ToolTip>
    <Description>Issues an Action Command to attached GEV devices on interface.</Description>
    <DisplayName>Action Command</DisplayName>
    <Visibility>Expert</Visibility>
    <pValue>ActionCommand_CtrlValueReg</pValue>
    <CommandValue>1</CommandValue>
  </Command>
  <IntReg Name="ActionCommand_CtrlValueReg">
    <Visibility>Invisible</Visibility>
    <Address>0x503C</Address>
    <Length>4</Length>
    <AccessMode>RW</AccessMode>
    <pPort>Device</pPort>
    <Sign>Signed</Sign>
    <Endianess>LittleEndian</Endianess>
  </IntReg>
</RegisterDescription>