<?xml version="1.0" encoding="UTF-8"?>
<RegisterDescription xmlns="http://www.genicam.org/GenApi/Version_1_1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.genicam.org/GenApi/Version_1_1 http://www.genicam.org/GenApi/GenApiSchema_Version_1_1.xsd" ModelName="SFNC_Camera" VendorName="Generic" ToolTip="Reference SFNC camera XML (autogenerated)" StandardNameSpace="None" SchemaMajorVersion="1" SchemaMinorVersion="1" SchemaSubMinorVersion="0" MajorVersion="1" MinorVersion="0" SubMinorVersion="0" ProductGuid="11111111-**************-************" VersionGuid="AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE">
  <Group Comment="RootCategory">
    <Category Name="Root" NameSpace="Standard">
      <!-- Mandatory element -->
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceInformation</pFeature>
      <pFeature>DeviceControl</pFeature>
    </Category>
  </Group>
  <Group Comment="SubCategories">
    <Category Name="DeviceInformation" NameSpace="Standard">
      <!-- Recommended element -->
      <ToolTip>Category that contains all Device Information features of the Device module.</ToolTip>
      <Description>Category that contains all Device Information features of the Device module.</Description>
      <DisplayName>Device Information</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceID</pFeature>
      <pFeature>DeviceSerialNumber</pFeature>
      <pFeature>DeviceVendorName</pFeature>
      <pFeature>DeviceModelName</pFeature>
      <pFeature>DeviceType</pFeature>
      <pFeature>DeviceDisplayName</pFeature>
      <pFeature>DeviceAccessStatus</pFeature>
      <pFeature>GUIXMLLocation</pFeature>
      <pFeature>GUIXMLPath</pFeature>
      <pFeature>GenICamXMLLocation</pFeature>
      <pFeature>GenICamXMLPath</pFeature>
      <pFeature>GevCCP</pFeature>
      <pFeature>GevDeviceIPAddress</pFeature>
      <pFeature>GevDeviceSubnetMask</pFeature>
      <pFeature>GevDeviceMACAddress</pFeature>
      <pFeature>GevDeviceGateway</pFeature>
      <pFeature>DeviceLinkSpeed</pFeature>
      <pFeature>DeviceVersion</pFeature>
      <pFeature>DeviceDriverVersion</pFeature>
      <pFeature>DeviceUserID</pFeature>
      <pFeature>GevVersionMajor</pFeature>
      <pFeature>GevVersionMinor</pFeature>
      <pFeature>GevDeviceModeIsBigEndian</pFeature>
      <pFeature>GevDeviceReadAndWriteTimeout</pFeature>
      <pFeature>GevDeviceMaximumRetryCount</pFeature>
      <pFeature>GevDevicePort</pFeature>
      <pFeature>GevDeviceDiscoverMaximumPacketSize</pFeature>
      <pFeature>GevDeviceMaximumPacketSize</pFeature>
    </Category>
    <Category Name="DeviceControl" NameSpace="Standard">
      <ToolTip>Category that contains all Device Control features of the Device module.</ToolTip>
      <Description>Category that contains all Device Control features of the Device module.</Description>
      <DisplayName>Device Control</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceMulticastMonitorMode</pFeature>
      <pFeature>DeviceEndianessMechanism</pFeature>
    </Category>
  </Group>
  <Group Comment="DeviceInformation">
    <StringReg Name="DeviceID" NameSpace="Standard">
      <ToolTip>Interface-wide unique identifier of this device.</ToolTip>
      <Description>Interface-wide unique identifier of this device.</Description>
      <DisplayName>Device ID  </DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x0000</Address>
      <Length>128</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <StringReg Name="DeviceSerialNumber" NameSpace="Standard">
      <ToolTip>Serial number of the remote device.</ToolTip>
      <Description>Serial number of the remote device.</Description>
      <DisplayName>Device Serial Number</DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x0000</Address>
      <Length>128</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <StringReg Name="DeviceVendorName" NameSpace="Standard">
      <ToolTip>Name of the remote device vendor.</ToolTip>
      <Description>Name of the remote device vendor.</Description>
      <DisplayName>Device Vendor Name  </DisplayName>
      <Visibility>Beginner</Visibility>
      <Address>0x0100</Address>
      <Length>128</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <StringReg Name="DeviceModelName" NameSpace="Standard">
      <ToolTip>Name of the remote device model.</ToolTip>
      <Description>Name of the remote device model.</Description>
      <DisplayName>Device Model Name</DisplayName>
      <Visibility>Beginner</Visibility>
      <Address>0x0180</Address>
      <Length>128</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Enumeration Name="DeviceType" NameSpace="Standard">
        <ToolTip>Transport layer type of the device.</ToolTip>
        <Description>Transport layer type of the device.</Description>
        <DisplayName>Device Type</DisplayName>
        <Visibility>Expert</Visibility>
        <ImposedAccessMode>RO</ImposedAccessMode>
        <EnumEntry Name="Mixed" NameSpace="Standard">
            <ToolTip>Transport Layer Type - Mixed.</ToolTip>
            <Description>TL - Mixed</Description>
            <DisplayName>Mixed</DisplayName>
            <Value>0</Value>
        </EnumEntry>
        <EnumEntry Name="Custom" NameSpace="Standard">
            <ToolTip>Transport Layer Type - Custom.</ToolTip>
            <Description>TL - Custom</Description>
            <DisplayName>Custom</DisplayName>
            <Value>1</Value>
        </EnumEntry>
        <EnumEntry Name="GEV" NameSpace="Standard">
            <ToolTip>Transport Layer Type - GEV.</ToolTip>
            <Description>TL - GEV</Description>
            <DisplayName>GEV</DisplayName>
            <Value>2</Value>
        </EnumEntry>
        <EnumEntry Name="CL" NameSpace="Standard">
            <ToolTip>Transport Layer Type - CL.</ToolTip>
            <Description>TL - CL</Description>
            <DisplayName>CL</DisplayName>
            <Value>3</Value>
        </EnumEntry>
        <EnumEntry Name="IIDC" NameSpace="Standard">
            <ToolTip>Transport Layer Type - IIDC.</ToolTip>
            <Description>TL - IIDC</Description>
            <DisplayName>IIDC</DisplayName>
            <Value>4</Value>
        </EnumEntry>
        <EnumEntry Name="UVC" NameSpace="Standard">
            <ToolTip>Transport Layer Type - UVC.</ToolTip>
            <Description>TL - UVC</Description>
            <DisplayName>UVC</DisplayName>
            <Value>5</Value>
        </EnumEntry>
        <EnumEntry Name="CXP" NameSpace="Standard">
            <ToolTip>Transport Layer Type - CXP.</ToolTip>
            <Description>TL - CXP</Description>
            <DisplayName>CXP</DisplayName>
            <Value>6</Value>
        </EnumEntry>
        <EnumEntry Name="CLHS" NameSpace="Standard">
            <ToolTip>Transport Layer Type - CLHS.</ToolTip>
            <Description>TL - CLHS</Description>
            <DisplayName>CLHS</DisplayName>
            <Value>7</Value>
        </EnumEntry>
        <EnumEntry Name="U3V" NameSpace="Standard">
            <ToolTip>Transport Layer Type - U3V.</ToolTip>
            <Description>TL - U3V</Description>
            <DisplayName>U3V</DisplayName>
            <Value>8</Value>
        </EnumEntry>
        <EnumEntry Name="ETHERNET" NameSpace="Standard">
            <ToolTip>Transport Layer Type - ETHERNET.</ToolTip>
            <Description>TL - ETHERNET</Description>
            <DisplayName>ETHERNET</DisplayName>
            <Value>9</Value>
        </EnumEntry>
        <EnumEntry Name="PCI" NameSpace="Standard">
            <ToolTip>Transport Layer Type - PCI.</ToolTip>
            <Description>TL - PCI</Description>
            <DisplayName>PCI</DisplayName>
            <Value>10</Value>
        </EnumEntry>
        <pValue>DeviceType_Val</pValue>
    </Enumeration>
    <IntReg Name="DeviceType_Val" NameSpace="Custom">
        <ToolTip>Transport layer type register.</ToolTip>
        <Description>Transport layer type register.</Description>
        <DisplayName>DeviceType Val</DisplayName>
        <Visibility>Invisible</Visibility>
        <Address>0x0080</Address>
        <Length>4</Length>
        <AccessMode>RO</AccessMode>
        <pPort>Device</pPort>
        <Sign>Signed</Sign>
        <Endianess>LittleEndian</Endianess>
    </IntReg>
    <StringReg Name="DeviceDisplayName" NameSpace="Standard">
      <ToolTip>User readable name of the device.</ToolTip>
      <Description>User readable name of the device. If this is not defined in the device this should be "VENDOR MODEL (ID)".</Description>
      <DisplayName>Device Display Name</DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x0200</Address>
      <Length>128</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Enumeration Name="GevCCP" NameSpace="Standard">
        <ToolTip>Controls the device access privilege of an application.</ToolTip>
        <Description>Controls the device access privilege of an application.</Description>
        <DisplayName>CCP</DisplayName>
        <Visibility>Beginner</Visibility>
        <ImposedAccessMode>RO</ImposedAccessMode>
    <EnumEntry Name="EnumEntry_GevCCP_OpenAccess" NameSpace="Standard">
        <Description>Open access privilege.</Description>
        <DisplayName>Open Access</DisplayName>
        <Value>0</Value>
        <Symbolic>OpenAccess</Symbolic>
    </EnumEntry>
    <EnumEntry Name="EnumEntry_GevCCP_ExclusiveAccess" NameSpace="Standard">
        <Description>Exclusive access privilege.</Description>
        <DisplayName>Exclusive Access</DisplayName>
        <Value>1</Value>
        <Symbolic>ExclusiveAccess</Symbolic>
    </EnumEntry>
    <EnumEntry Name="EnumEntry_GevCCP_ControlAccess" NameSpace="Standard">
        <Description>Control access privilege.</Description>
        <DisplayName>Control Access</DisplayName>
        <Value>2</Value>
        <Symbolic>ControlAccess</Symbolic>
    </EnumEntry>
    <pValue>GevCCP_Val</pValue>
    </Enumeration>
    <IntReg Name="GevCCP_Val" NameSpace="Custom">
        <ToolTip>Control Channel Privilege register.</ToolTip>
        <Description>Control Channel Privilege register.</Description>
        <DisplayName>Gev CCP Val</DisplayName>
        <Visibility>Invisible</Visibility>
        <Address>0x4060</Address>
        <Length>4</Length>
        <AccessMode>RO</AccessMode>
        <pPort>Device</pPort>
        <Sign>Unsigned</Sign>
        <Endianess>BigEndian</Endianess>
    </IntReg>
    <Enumeration Name="DeviceAccessStatus" NameSpace="Standard">
      <ToolTip>Gets the access status the transport layer Producer has on the device.</ToolTip>
      <Description>Gets the access status the transport layer Producer has on the device.</Description>
      <DisplayName>Device Access Status</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Unknown" NameSpace="Standard">
        <ToolTip>Unknown status.</ToolTip>
        <Description>Unknown status</Description>
        <DisplayName>Unknown</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="ReadWrite" NameSpace="Standard">
        <ToolTip>Full access.</ToolTip>
        <Description>Full access</Description>
        <DisplayName>Read Write</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="ReadOnly" NameSpace="Standard">
        <ToolTip>Read-only access.</ToolTip>
        <Description>Read-only access</Description>
        <DisplayName>Read Only</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <EnumEntry Name="NoAccess" NameSpace="Standard">
        <ToolTip>Non-available devices.</ToolTip>
        <Description>Non-available devices</Description>
        <DisplayName>No Access</DisplayName>
        <Value>3</Value>
      </EnumEntry>
      <pValue>DeviceAccessStatus_RegVal</pValue>
    </Enumeration>
    <IntReg Name="DeviceAccessStatus_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x2000</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Enumeration Name="GUIXMLLocation" NameSpace="Standard">
      <ToolTip>Sets the location to load GUI XML.</ToolTip>
      <Description>Sets the location to load GUI XML.</Description>
      <DisplayName>GUI XML Source</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Device" NameSpace="Standard">
        <ToolTip>Load XML from device.</ToolTip>
        <Description>Load XML from device</Description>
        <DisplayName>Device</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="Host" NameSpace="Standard">
        <ToolTip>Load XML from host.</ToolTip>
        <Description>Load XML from host</Description>
        <DisplayName>Host</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <pValue>GUIXMLLocation_RegVal</pValue>
    </Enumeration>
     <IntReg Name="GUIXMLLocation_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4068</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <StringReg Name="GUIXMLPath" NameSpace="Standard">
      <ToolTip>GUI XML Path.</ToolTip>
      <Description>GUI XML Path.</Description>
      <DisplayName>GUI XML Path</DisplayName>
      <Visibility>Beginner</Visibility>
      <Address>0x4092</Address>
      <Length>256</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Enumeration Name="GenICamXMLLocation" NameSpace="Standard">
      <ToolTip>Sets the location to load GenICam XML.</ToolTip>
      <Description>Sets the location to load GenICam XML.</Description>
      <DisplayName>GenICam XML Source</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Device" NameSpace="Standard">
        <ToolTip>Load GenICam XML from device.</ToolTip>
        <Description>Load GenICam XML from device</Description>
        <DisplayName>Device</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="Host" NameSpace="Standard">
        <ToolTip>Load GenICam XML from host.</ToolTip>
        <Description>Load GenICam XML from host</Description>
        <DisplayName>Host</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <pValue>GenICamXMLLocation_RegVal</pValue>
    </Enumeration>
     <IntReg Name="GenICamXMLLocation_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4076</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <StringReg Name="GenICamXMLPath" NameSpace="Standard">
      <ToolTip>GenICam XML Path.</ToolTip>
      <Description>GenICam XML Path.</Description>
      <DisplayName>GenICam XML Path</DisplayName>
      <Visibility>Beginner</Visibility>
      <Address>0x4084</Address>
      <Length>256</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Integer Name="GevDeviceIPAddress" NameSpace="Standard">
      <ToolTip>Current IP address of the GVCP interface of the selected remote device.</ToolTip>
      <Description>Current IP address of the GVCP interface of the selected remote device.</Description>
      <DisplayName>Gev Device IP Address</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceIPAddress_RegVal</pValue>
      <Representation>HexNumber</Representation>
    </Integer>
    <IntReg Name="GevDeviceIPAddress_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4000</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDeviceSubnetMask" NameSpace="Standard">
      <ToolTip>Current subnet mask of the GVCP interface of the selected remote device.</ToolTip>
      <Description>Current subnet mask of the GVCP interface of the selected remote device.</Description>
      <DisplayName>Gev Device Subnet Mask</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceSubnetMask_RegVal</pValue>
      <Representation>HexNumber</Representation>
    </Integer>
    <IntReg Name="GevDeviceSubnetMask_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4008</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDeviceMACAddress" NameSpace="Standard">
      <ToolTip>48-bit MAC address of the GVCP interface of the selected remote device.</ToolTip>
      <Description>48-bit MAC address of the GVCP interface of the selected remote device.</Description>
      <DisplayName>Gev Device MAC Address</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceMACAddress_RegVal</pValue>
      <Representation>HexNumber</Representation>
    </Integer>
    <IntReg Name="GevDeviceMACAddress_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4028</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDeviceGateway" NameSpace="Standard">
      <ToolTip>Current gateway IP address of the GVCP interface of the remote device.</ToolTip>
      <Description>Current gateway IP address of the GVCP interface of the remote device.</Description>
      <DisplayName>Gev Device Gateway</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceGateway_RegVal</pValue>
      <Representation>HexNumber</Representation>
    </Integer>
    <IntReg Name="GevDeviceGateway_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4016</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="DeviceLinkSpeed" NameSpace="Standard">
      <ToolTip>Indicates the speed of transmission negotiated by the given network interface in Mbps.</ToolTip>
      <Description>Indicates the speed of transmission negotiated by the given network interface in Mbps.</Description>
      <DisplayName>Gev Device Link Speed</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>DeviceLinkSpeed_RegVal</pValue>
      <Representation>PureNumber</Representation>
    </Integer>
    <IntReg Name="DeviceLinkSpeed_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4036</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>BigEndian</Endianess>
    </IntReg>
    <StringReg Name="DeviceVersion" NameSpace="Standard">
      <ToolTip>Version of the device.</ToolTip>
      <Description>Version of the device.</Description>
      <DisplayName>Device Version</DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x2008</Address>
      <Length>64</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <StringReg Name="DeviceDriverVersion" NameSpace="Standard">
      <ToolTip>Version of the device driver.</ToolTip>
      <Description>Version of the device driver.</Description>
      <DisplayName>Device Driver Version</DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x2048</Address>
      <Length>64</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <StringReg Name="DeviceUserID" NameSpace="Standard">
      <ToolTip>User Defined Name.</ToolTip>
      <Description>User Defined Name.</Description>
      <DisplayName>Device User ID</DisplayName>
      <Visibility>Expert</Visibility>
      <Address>0x2088</Address>
      <Length>64</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Integer Name="GevVersionMajor" NameSpace="Standard">
      <ToolTip> Major version of the specification.</ToolTip>
      <Description> Major version of the specification.</Description>
      <DisplayName>Gev Version Major</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevVersionMajor_RegVal</pValue>
    </Integer>
    <MaskedIntReg Name="GevVersionMajor_RegVal" NameSpace="Standard">
      <Visibility>Expert</Visibility>
      <Address>0x4044</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <LSB>15</LSB>
      <MSB>0</MSB>
      <Sign>Unsigned</Sign>
      <Endianess>BigEndian</Endianess>
    </MaskedIntReg>
    <Integer Name="GevVersionMinor" NameSpace="Standard">
      <ToolTip> Minor version of the specification.</ToolTip>
      <Description> Minor version of the specification.</Description>
      <DisplayName>Gev Version Minor</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevVersionMinor_RegVal</pValue>
    </Integer>
    <MaskedIntReg Name="GevVersionMinor_RegVal" NameSpace="Standard">
      <Visibility>Expert</Visibility>
      <Address>0x4044</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <LSB>31</LSB>
      <MSB>16</MSB>
      <Sign>Unsigned</Sign>
      <Endianess>BigEndian</Endianess>
    </MaskedIntReg>
    <Boolean Name="GevDeviceModeIsBigEndian" NameSpace="Standard">
      <ToolTip>This represents the endianness of all device's registers (bootstrap registers and manufacturer-specific registers).</ToolTip>
      <Description>This represents the endianness of all device's registers (bootstrap registers and manufacturer-specific registers).</Description>
      <DisplayName>Gev Device Mode Is Big Endian</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceModeIsBigEndian_RegVal</pValue>
    </Boolean>
    <IntReg Name="GevDeviceModeIsBigEndian_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4100</Address>
      <Length>4</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
     <Integer Name="GevDeviceReadAndWriteTimeout" NameSpace="Standard">
      <ToolTip>The timeout in us for read/write operations to the camera.</ToolTip>
      <Description>The timeout in us for read/write operations to the camera.</Description>
      <DisplayName>Read And Write Timeout</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>GevDeviceReadAndWriteTimeout_RegVal</pValue>
      <Min>20000</Min>
      <Max>1000000</Max>
    </Integer>
    <IntReg Name="GevDeviceReadAndWriteTimeout_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4108</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDeviceMaximumRetryCount" NameSpace="Standard">
      <ToolTip>Maximum number of times to retry a read/write operation.</ToolTip>
      <Description>Maximum number of times to retry a read/write operation.</Description>
      <DisplayName>Maximum Retry Count</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>GevDeviceMaximumRetryCount_RegVal</pValue>
    </Integer>
    <IntReg Name="GevDeviceMaximumRetryCount_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4110</Address>
      <Length>8</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDevicePort" NameSpace="Standard">
      <ToolTip>Current IP port of the GVCP interface of the selected remote device.</ToolTip>
      <Description>Current IP port of the GVCP interface of the selected remote device.</Description>
      <DisplayName>Gev Device Port</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDevicePort_RegVal</pValue>
      <Representation>PureNumber</Representation>
    </Integer>
    <IntReg Name="GevDevicePort_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4118</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Command Name="GevDeviceDiscoverMaximumPacketSize" NameSpace="Standard">
      <ToolTip>Discovers and updates the maximum packet size that can be safely used by the device on the current interface.</ToolTip>
      <Description>Discovers and updates the maximum packet size that can be safely used by the device on the current interface.</Description>
      <DisplayName>Discover Maximum Packet Size</DisplayName>
      <Visibility>Expert</Visibility>
      <pValue>GevDeviceDiscoverMaximumPacketSize_CtrlValueReg</pValue>
      <CommandValue>1</CommandValue>
    </Command>
    <IntReg Name="GevDeviceDiscoverMaximumPacketSize_CtrlValueReg">
      <Visibility>Invisible</Visibility>
      <Address>0x4120</Address>
      <Length>8</Length>
      <AccessMode>WO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Signed</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Integer Name="GevDeviceMaximumPacketSize" NameSpace="Standard">
      <ToolTip>The maximum packet size that can be safely used by the device on the current interface.</ToolTip>
      <Description>The maximum packet size that can be safely used by the device on the current interface.</Description>
      <DisplayName>Maximum Packet Size</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pValue>GevDeviceMaximumPacketSize_RegVal</pValue>
    </Integer>
    <IntReg Name="GevDeviceMaximumPacketSize_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4128</Address>
      <Length>8</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
  </Group>
  <Group Comment="DeviceControl">
    <Boolean Name="DeviceMulticastMonitorMode" NameSpace="Standard">
      <ToolTip>Controls and indicates if the device is operating in as a Multicast Monitor.</ToolTip>
      <Description>Controls and indicates if the device is operating in as a Multicast Monitor.</Description>
      <DisplayName>Multicast Monitor Mode</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <pValue>DeviceMulticastMonitorMode_RegVal</pValue>
    </Boolean>
    <IntReg Name="DeviceMulticastMonitorMode_RegVal" NameSpace="Standard">
      <Visibility>Invisible</Visibility>
      <Address>0x4052</Address>
      <Length>4</Length>
      <AccessMode>RW</AccessMode>
      <pPort>Device</pPort>
      <Sign>Unsigned</Sign>
      <Endianess>LittleEndian</Endianess>
    </IntReg>
    <Enumeration Name="DeviceEndianessMechanism" NameSpace="Standard">
      <ToolTip>Identifies the endianness handling mode.</ToolTip>
      <Description>Identifies the endianness handling mode.</Description>
      <DisplayName>Device Endianness Mechanism</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Legacy" NameSpace="Standard">
        <ToolTip>Handling the device endianness according to GenICam Schema 1.</ToolTip>
        <Description>Handling the device endianness according to GenICam Schema 1.0</Description>
        <DisplayName>Legacy</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="Standard" NameSpace="Standard">
        <ToolTip>Handling the device endianness according to GenICam Schema 1.</ToolTip>
        <Description>Handling the device endianness according to GenICam Schema 1.1 and later</Description>
        <DisplayName>Standard</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <Value>1</Value>
    </Enumeration>
  </Group>
  <Group Comment="SpecialFeatures">
    <Port Name="Device" NameSpace="Standard">
    </Port>
  </Group>
</RegisterDescription>
