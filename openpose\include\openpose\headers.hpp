#ifndef OPENPOSE_HEADERS_HPP
#define OPENPOSE_HEADERS_HPP

// 3d module
#include <openpose/3d/headers.hpp>

// calibration module
#include <openpose/calibration/headers.hpp>

// core module
#include <openpose/core/headers.hpp>

// face module
#include <openpose/face/headers.hpp>

// filestream module
#include <openpose/filestream/headers.hpp>

// gui module
#include <openpose/gui/headers.hpp>

// hand module
#include <openpose/hand/headers.hpp>

// net module
#include <openpose/net/headers.hpp>

// pose module
#include <openpose/pose/headers.hpp>

// producer module
#include <openpose/producer/headers.hpp>

// threading module
#include <openpose/thread/headers.hpp>

// tracking module
#include <openpose/tracking/headers.hpp>

// unity module
#include <openpose/unity/headers.hpp>

// utilities module
#include <openpose/utilities/headers.hpp>

// wrapper module
#include <openpose/wrapper/headers.hpp>

#endif // OPENPOSE_HEADERS_HPP
