#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenPose 設置和測試腳本
"""

import os
import sys
import zipfile
import shutil
from pathlib import Path

class OpenPoseSetup:
    def __init__(self):
        self.current_dir = Path.cwd()
        self.openpose_zip = None
        self.openpose_dir = None
        
    def find_openpose_zip(self):
        """尋找 OpenPose ZIP 檔案"""
        possible_names = [
            "openpose-1.7.0-binaries-win64-gpu-python3.7-flir-3d_recommended.zip",
            "openpose-1.7.0-gpu-full.zip",
            "openpose-gpu.zip"
        ]
        
        for name in possible_names:
            zip_path = self.current_dir / name
            if zip_path.exists():
                file_size = zip_path.stat().st_size
                print(f"📁 找到檔案: {name}")
                print(f"📊 檔案大小: {file_size / (1024*1024):.1f} MB")
                
                if file_size > 400 * 1024 * 1024:  # 大於 400MB
                    print("✅ 檔案大小正確")
                    self.openpose_zip = zip_path
                    return True
                else:
                    print("⚠️ 檔案大小異常，可能下載不完整")
                    
        print("❌ 未找到 OpenPose ZIP 檔案")
        return False
    
    def extract_openpose(self):
        """解壓縮 OpenPose"""
        if not self.openpose_zip:
            return False
            
        print(f"📦 開始解壓縮: {self.openpose_zip.name}")
        
        try:
            with zipfile.ZipFile(self.openpose_zip, 'r') as zip_ref:
                # 獲取解壓縮後的資料夾名稱
                first_file = zip_ref.namelist()[0]
                root_folder = first_file.split('/')[0]
                
                print(f"📂 解壓縮到: {root_folder}")
                zip_ref.extractall(self.current_dir)
                
                self.openpose_dir = self.current_dir / root_folder
                print(f"✅ 解壓縮完成: {self.openpose_dir}")
                return True
                
        except Exception as e:
            print(f"❌ 解壓縮失敗: {e}")
            return False
    
    def setup_python_path(self):
        """設置 Python 路徑"""
        if not self.openpose_dir:
            return False
            
        # 尋找 Python 模組路徑
        python_paths = [
            self.openpose_dir / "python" / "openpose" / "Release",
            self.openpose_dir / "python" / "openpose",
            self.openpose_dir / "python",
        ]
        
        for path in python_paths:
            if path.exists():
                print(f"📍 找到 Python 模組路徑: {path}")
                
                # 添加到 Python 路徑
                if str(path) not in sys.path:
                    sys.path.insert(0, str(path))
                    print(f"✅ 已添加到 Python 路徑")
                
                return True
        
        print("❌ 未找到 Python 模組路徑")
        return False
    
    def test_openpose_import(self):
        """測試 OpenPose 導入"""
        print("🧪 測試 OpenPose 導入...")
        
        try:
            import openpose as op
            print("✅ OpenPose 導入成功！")
            return True
        except ImportError as e:
            print(f"❌ OpenPose 導入失敗: {e}")
            return False
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
            return False
    
    def create_test_script(self):
        """創建測試腳本"""
        test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenPose 測試腳本
"""

import sys
import os
from pathlib import Path

# 添加 OpenPose 路徑
openpose_dir = Path.cwd() / "openpose-1.7.0-binaries-win64-gpu"
python_path = openpose_dir / "python" / "openpose" / "Release"
if python_path.exists():
    sys.path.insert(0, str(python_path))

try:
    import openpose as op
    print("✅ OpenPose 導入成功！")
    
    # 創建 OpenPose 參數
    params = dict()
    params["model_folder"] = str(openpose_dir / "models")
    params["face"] = False
    params["hand"] = True
    
    # 初始化 OpenPose
    opWrapper = op.WrapperPython()
    opWrapper.configure(params)
    opWrapper.start()
    
    print("🎉 OpenPose 初始化成功！")
    print("🎯 準備好進行螺絲檢測了！")
    
except Exception as e:
    print(f"❌ 錯誤: {e}")
'''
        
        with open("test_openpose.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        print("📝 已創建測試腳本: test_openpose.py")
    
    def run_setup(self):
        """運行完整設置"""
        print("🚀 OpenPose 設置開始")
        print("=" * 50)
        
        # 1. 尋找 ZIP 檔案
        if not self.find_openpose_zip():
            print("\n💡 請確認已下載 OpenPose ZIP 檔案到當前目錄")
            return False
        
        # 2. 解壓縮
        if not self.extract_openpose():
            return False
        
        # 3. 設置路徑
        if not self.setup_python_path():
            print("⚠️ 路徑設置失敗，但可以手動設置")
        
        # 4. 測試導入
        self.test_openpose_import()
        
        # 5. 創建測試腳本
        self.create_test_script()
        
        print("\n" + "=" * 50)
        print("🎉 OpenPose 設置完成！")
        print("\n📋 下一步:")
        print("1. 運行: python test_openpose.py")
        print("2. 如果成功，就可以開始真正的螺絲檢測了！")
        
        return True

if __name__ == "__main__":
    setup = OpenPoseSetup()
    setup.run_setup()
