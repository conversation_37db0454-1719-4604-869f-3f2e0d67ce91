#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenPose GPU 版本下載器
"""

import requests
import os
from tqdm import tqdm

def download_openpose_gpu():
    """下載 OpenPose GPU 版本"""
    url = "https://sourceforge.net/projects/openpose.mirror/files/v1.7.0/openpose-1.7.0-binaries-win64-gpu-python3.7-flir-3d_recommended.zip/download"
    filename = "openpose-1.7.0-gpu-full.zip"
    
    print("🚀 開始下載 OpenPose GPU 版本...")
    print(f"📁 檔案名稱: {filename}")
    print(f"🔗 下載連結: {url}")
    print()
    
    try:
        # 發送請求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, stream=True, allow_redirects=True)
        response.raise_for_status()
        
        # 獲取檔案大小
        total_size = int(response.headers.get('content-length', 0))
        
        print(f"📊 檔案大小: {total_size / (1024*1024):.1f} MB")
        print()
        
        # 下載檔案
        with open(filename, 'wb') as file, tqdm(
            desc="下載進度",
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as progress_bar:
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    progress_bar.update(len(chunk))
        
        print(f"\n✅ 下載完成！")
        print(f"📁 檔案位置: {os.path.abspath(filename)}")
        
        # 檢查檔案大小
        file_size = os.path.getsize(filename)
        print(f"📊 實際檔案大小: {file_size / (1024*1024):.1f} MB")
        
        if file_size > 400 * 1024 * 1024:  # 大於 400MB
            print("🎉 檔案大小正確，下載成功！")
            return True
        else:
            print("⚠️ 檔案大小異常，可能下載不完整")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 下載失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        return False

if __name__ == "__main__":
    success = download_openpose_gpu()
    if success:
        print("\n🎯 下一步：解壓縮檔案並設置 OpenPose")
    else:
        print("\n💡 建議：請手動從瀏覽器下載")
        print("   連結: https://sourceforge.net/projects/openpose.mirror/files/v1.7.0/")
