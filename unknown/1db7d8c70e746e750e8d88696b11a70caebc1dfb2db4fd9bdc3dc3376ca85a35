import cv2
import numpy as np
import os
import sys
import time
import json
from datetime import datetime
from collections import deque
import math

# OpenPose 導入 (需要先安裝 OpenPose)
try:
    # 設置 OpenPose 路徑 (根據您的安裝路徑調整)
    sys.path.append('/path/to/openpose/build/python')
    from openpose import pyopenpose as op
    OPENPOSE_AVAILABLE = True
except ImportError:
    print("警告: OpenPose 未安裝，使用模擬模式")
    OPENPOSE_AVAILABLE = False

class AssemblySOPMonitor:
    def __init__(self, model_folder="models/", log_file="assembly_log.json", sop_config_file="sop_config.json"):
        """
        初始化冷氣總裝 SOP 監控系統

        Args:
            model_folder: OpenPose 模型資料夾路徑
            log_file: 日誌檔案路徑
            sop_config_file: SOP 配置檔案路徑
        """
        self.model_folder = model_folder
        self.log_file = log_file
        self.sop_config_file = sop_config_file
        self.frame_count = 0

        # 預設 SOP 步驟定義 (如果配置檔案載入失敗時使用)
        self.default_sop_steps = [
            "準備工作區",
            "檢查零件",
            "安裝冷凝器",
            "鎖緊螺絲",
            "連接管路",
            "檢查連接",
            "最終檢測"
        ]

        # 動作識別閾值 (會從配置檔案更新)
        self.action_thresholds = {
            'screwing': 0.7,
            'rotating': 0.6,
            'reaching': 0.5,
            'grasping': 0.8
        }

        # 載入 SOP 配置
        self.load_sop_config()
        
        # 當前狀態
        self.current_step = 0
        self.action_history = deque(maxlen=30)  # 記錄最近30幀的動作
        self.error_count = 0
        self.logs = []

        # 螺絲計數相關
        self.screw_count = 0  # 已檢測到的螺絲數量
        self.last_screw_time = 0  # 上次檢測到螺絲的時間
        self.screw_detection_active = False  # 是否正在進行螺絲動作
        self.screw_start_time = 0  # 螺絲動作開始時間
        self.target_screw_count = 16  # 目標螺絲數量

        # 螺絲計數相關
        self.screw_count = 0  # 已檢測到的螺絲數量
        self.last_screw_time = 0  # 上次檢測到螺絲的時間
        self.screw_detection_active = False  # 是否正在進行螺絲動作
        self.screw_start_time = 0  # 螺絲動作開始時間
        
        # 初始化 OpenPose
        self.init_openpose()

    def load_sop_config(self):
        """載入 SOP 配置檔案"""
        try:
            with open(self.sop_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 載入 SOP 步驟
            self.sop_steps = [step['name'] for step in config['sop_steps']]
            self.sop_details = {i: step for i, step in enumerate(config['sop_steps'])}

            # 載入動作閾值
            if 'action_types' in config:
                for action, details in config['action_types'].items():
                    if action in self.action_thresholds:
                        self.action_thresholds[action] = details['threshold']

            print(f"✅ 成功載入 SOP 配置: {len(self.sop_steps)} 個步驟")

        except FileNotFoundError:
            print(f"⚠️  配置檔案 {self.sop_config_file} 不存在，使用預設配置")
            self.sop_steps = self.default_sop_steps
            self.sop_details = {}
        except json.JSONDecodeError as e:
            print(f"⚠️  配置檔案格式錯誤: {e}，使用預設配置")
            self.sop_steps = self.default_sop_steps
            self.sop_details = {}
        except Exception as e:
            print(f"⚠️  載入配置檔案時發生錯誤: {e}，使用預設配置")
            self.sop_steps = self.default_sop_steps
            self.sop_details = {}
        
    def init_openpose(self):
        """初始化 OpenPose"""
        if not OPENPOSE_AVAILABLE:
            print("使用模擬模式運行")
            return
            
        try:
            # OpenPose 參數設定
            params = dict()
            params["model_folder"] = self.model_folder
            params["face"] = False  # 不檢測臉部以提高效能
            params["hand"] = True   # 檢測手部關鍵點
            params["net_resolution"] = "-1x368"  # 降低解析度提高速度
            params["scale_number"] = 1
            params["scale_gap"] = 0.3
            params["render_pose"] = 1
            
            # 建立 OpenPose 包裝器
            self.opWrapper = op.WrapperPython()
            self.opWrapper.configure(params)
            self.opWrapper.start()
            
            print("OpenPose 初始化成功")
            
        except Exception as e:
            print(f"OpenPose 初始化失敗: {e}")
            self.opWrapper = None
    
    def detect_pose(self, frame):
        """
        檢測人體姿態
        
        Args:
            frame: 輸入影像
            
        Returns:
            dict: 包含姿態關鍵點的字典
        """
        if not OPENPOSE_AVAILABLE or self.opWrapper is None:
            # 模擬模式：返回虛擬關鍵點
            return self.simulate_pose_data()
        
        try:
            # 處理影像
            datum = op.Datum()
            datum.cvInputData = frame
            self.opWrapper.emplaceAndPop(op.VectorDatum([datum]))
            
            # 提取關鍵點
            result = {
                'pose_keypoints': datum.poseKeypoints,
                'hand_keypoints_left': datum.handKeypoints[0] if datum.handKeypoints is not None and len(datum.handKeypoints) > 0 else None,
                'hand_keypoints_right': datum.handKeypoints[1] if datum.handKeypoints is not None and len(datum.handKeypoints) > 1 else None,
                'output_image': datum.cvOutputData
            }
            
            return result
            
        except Exception as e:
            print(f"姿態檢測錯誤: {e}")
            return None
    
    def simulate_pose_data(self):
        """模擬姿態數據（用於測試）"""
        # 模擬基本的手部和身體關鍵點
        pose_points = np.random.rand(25, 3) * 100  # 25個身體關鍵點
        hand_left = np.random.rand(21, 3) * 50     # 21個左手關鍵點
        hand_right = np.random.rand(21, 3) * 50    # 21個右手關鍵點
        
        return {
            'pose_keypoints': pose_points,
            'hand_keypoints_left': hand_left,
            'hand_keypoints_right': hand_right,
            'output_image': None
        }
    
    def analyze_screwing_action(self, hand_keypoints):
        """
        分析螺絲動作

        Args:
            hand_keypoints: 手部關鍵點

        Returns:
            float: 螺絲動作的信心分數 (0-1)
        """
        if hand_keypoints is None:
            # 在模擬模式下，隨機生成螺絲動作信心分數
            import random
            confidence = random.uniform(0.6, 0.9)  # 模擬螺絲動作
            return confidence

        try:
            # 獲取關鍵的手部點位
            wrist = hand_keypoints[0]      # 手腕
            thumb_tip = hand_keypoints[4]  # 拇指尖
            index_tip = hand_keypoints[8]  # 食指尖
            middle_tip = hand_keypoints[12] # 中指尖

            # 計算手指間距離（判斷抓握）
            thumb_index_dist = np.linalg.norm(thumb_tip[:2] - index_tip[:2])

            # 計算手腕旋轉角度變化
            if len(self.action_history) > 5:
                prev_wrist = self.action_history[-5].get('wrist_pos', wrist[:2])
                wrist_movement = np.linalg.norm(wrist[:2] - prev_wrist)

                # 螺絲動作特徵：小幅旋轉 + 握拳
                if thumb_index_dist < 30 and wrist_movement < 10:
                    return 0.8

            return 0.3

        except Exception as e:
            print(f"螺絲動作分析錯誤: {e}")
            return 0.0
    
    def analyze_rotating_action(self, pose_keypoints, hand_keypoints):
        """
        分析旋轉動作
        
        Args:
            pose_keypoints: 身體關鍵點
            hand_keypoints: 手部關鍵點
            
        Returns:
            float: 旋轉動作的信心分數 (0-1)
        """
        if pose_keypoints is None or hand_keypoints is None:
            return 0.0
        
        try:
            # 獲取肩膀和手肘位置
            right_shoulder = pose_keypoints[2]
            right_elbow = pose_keypoints[3]
            left_shoulder = pose_keypoints[5]
            left_elbow = pose_keypoints[6]
            
            # 計算手臂角度
            right_arm_angle = math.atan2(right_elbow[1] - right_shoulder[1], 
                                       right_elbow[0] - right_shoulder[0])
            left_arm_angle = math.atan2(left_elbow[1] - left_shoulder[1], 
                                      left_elbow[0] - left_shoulder[0])
            
            # 檢查手臂旋轉模式
            if len(self.action_history) > 10:
                prev_angles = [action.get('arm_angles', [0, 0]) for action in list(self.action_history)[-10:]]
                angle_variance = np.var([angles[0] for angles in prev_angles])
                
                if angle_variance > 0.1:  # 手臂有明顯旋轉
                    return 0.75
            
            return 0.2
            
        except Exception as e:
            print(f"旋轉動作分析錯誤: {e}")
            return 0.0

    def detect_screw_action(self, screwing_confidence, current_time):
        """
        檢測螺絲動作並計數

        Args:
            screwing_confidence: 螺絲動作的信心分數
            current_time: 當前時間 (秒)

        Returns:
            bool: 是否檢測到新的螺絲動作
        """
        screw_threshold = self.action_thresholds.get('screwing', 0.7)
        min_screw_duration = 0.8  # 最短螺絲動作時間
        max_gap_time = 5.0  # 兩次螺絲動作間最大間隔

        # 檢測螺絲動作開始
        if screwing_confidence > screw_threshold and not self.screw_detection_active:
            self.screw_detection_active = True
            self.screw_start_time = current_time
            return False

        # 檢測螺絲動作結束
        elif screwing_confidence <= screw_threshold and self.screw_detection_active:
            screw_duration = current_time - self.screw_start_time

            # 如果螺絲動作持續時間合理，計為一次螺絲動作
            if screw_duration >= min_screw_duration:
                self.screw_count += 1
                self.last_screw_time = current_time
                self.screw_detection_active = False

                # 記錄螺絲檢測事件
                self.log_event('screw_detected', {
                    'screw_number': self.screw_count,
                    'duration': screw_duration,
                    'confidence': screwing_confidence,
                    'time': current_time
                })

                print(f"🔩 檢測到第 {self.screw_count} 顆螺絲 (持續 {screw_duration:.1f}秒)")
                return True
            else:
                self.screw_detection_active = False

        return False

    def check_screw_completion(self):
        """檢查螺絲是否全部完成"""
        if self.screw_count >= self.target_screw_count:
            if self.screw_count == self.target_screw_count:
                self.log_event('screw_complete', f"✅ 所有 {self.target_screw_count} 顆螺絲已完成")
                print(f"✅ 螺絲作業完成！共檢測到 {self.screw_count} 顆螺絲")
            else:
                self.log_event('screw_excess', f"⚠️ 檢測到多餘螺絲：{self.screw_count}/{self.target_screw_count}")
                print(f"⚠️ 警告：檢測到 {self.screw_count} 顆螺絲，超過目標 {self.target_screw_count} 顆")
            return True
        return False

    def check_sop_sequence(self, detected_actions):
        """
        檢查 SOP 序列是否正確
        
        Args:
            detected_actions: 檢測到的動作字典
            
        Returns:
            dict: 包含序列檢查結果的字典
        """
        result = {
            'current_step': self.current_step,
            'expected_action': self.sop_steps[self.current_step],
            'detected_action': max(detected_actions, key=detected_actions.get),
            'confidence': max(detected_actions.values()),
            'sequence_correct': True,
            'error_message': None
        }
        
        # 根據當前步驟檢查預期動作
        if self.current_step in self.sop_details:
            step_actions = self.sop_details[self.current_step]['expected_actions']
        else:
            # 預設動作對應 (如果配置檔案沒有載入)
            expected_actions = {
                0: ['reaching', 'grasping'],      # 準備工作區
                1: ['grasping', 'rotating'],      # 檢查零件
                2: ['grasping', 'rotating'],      # 安裝冷凝器
                3: ['screwing'],                  # 鎖緊螺絲
                4: ['grasping', 'rotating'],      # 連接管路
                5: ['reaching', 'grasping'],      # 檢查連接
                6: ['reaching']                   # 最終檢測
            }
            step_actions = expected_actions.get(self.current_step, [])
        
        if result['detected_action'] in step_actions and result['confidence'] > 0.6:
            # 動作正確，可能進入下一步
            if self.current_step < len(self.sop_steps) - 1:
                self.current_step += 1
        elif result['confidence'] > 0.7:
            # 檢測到不正確的動作
            result['sequence_correct'] = False
            result['error_message'] = f"步驟 {self.current_step + 1} 應執行 {step_actions}，但檢測到 {result['detected_action']}"
            self.error_count += 1
        
        return result
    
    def log_event(self, event_type, details):
        """
        記錄事件日誌
        
        Args:
            event_type: 事件類型
            details: 事件詳情
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'frame': self.frame_count,
            'event_type': event_type,
            'details': details
        }
        
        self.logs.append(log_entry)
        
        # 即時輸出重要事件
        if event_type == 'error':
            print(f"⚠️  錯誤 - 第 {self.frame_count} 幀: {details}")
        elif event_type == 'step_complete':
            print(f"✅ 步驟完成 - 第 {self.frame_count} 幀: {details}")
    
    def save_logs(self):
        """儲存日誌到檔案"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(self.logs, f, ensure_ascii=False, indent=2)
            print(f"日誌已儲存至 {self.log_file}")
        except Exception as e:
            print(f"日誌儲存失敗: {e}")
    
    def process_frame(self, frame):
        """
        處理單一影格

        Args:
            frame: 輸入影像

        Returns:
            dict: 處理結果
        """
        self.frame_count += 1
        current_time = self.frame_count / 30.0  # 假設30fps

        # 檢測姿態
        pose_result = self.detect_pose(frame)
        if pose_result is None:
            return None

        # 分析動作
        detected_actions = {
            'screwing': self.analyze_screwing_action(pose_result['hand_keypoints_right']),
            'rotating': self.analyze_rotating_action(pose_result['pose_keypoints'], pose_result['hand_keypoints_right']),
            'reaching': 0.3,  # 簡化實現
            'grasping': 0.4   # 簡化實現
        }

        # 螺絲檢測
        screw_detected = self.detect_screw_action(detected_actions['screwing'], current_time)

        # 檢查螺絲完成狀態
        screw_complete = self.check_screw_completion()

        # 檢查 SOP 序列
        sop_result = self.check_sop_sequence(detected_actions)

        # 記錄動作歷史
        action_data = {
            'frame': self.frame_count,
            'actions': detected_actions,
            'wrist_pos': pose_result['hand_keypoints_right'][0][:2] if pose_result['hand_keypoints_right'] is not None else None,
            'arm_angles': [0, 0],  # 簡化實現
            'screw_count': self.screw_count,
            'time': current_time
        }
        self.action_history.append(action_data)

        # 記錄事件
        if not sop_result['sequence_correct']:
            self.log_event('error', sop_result['error_message'])
        elif sop_result['confidence'] > 0.8:
            self.log_event('action_detected', f"檢測到 {sop_result['detected_action']}")

        return {
            'pose_result': pose_result,
            'detected_actions': detected_actions,
            'sop_result': sop_result,
            'frame_count': self.frame_count,
            'screw_count': self.screw_count,
            'screw_detected': screw_detected,
            'screw_complete': screw_complete,
            'current_time': current_time
        }
    
    def run_video_monitoring(self, video_path=0):
        """
        運行影片監控
        
        Args:
            video_path: 影片路徑或攝像頭索引 (0 為預設攝像頭)
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"無法開啟影片: {video_path}")
            return
        
        print("開始監控冷氣總裝 SOP...")
        print("按 'q' 鍵退出監控")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 處理影格
                result = self.process_frame(frame)
                
                if result is not None:
                    # 在影像上繪製資訊
                    if result['pose_result']['output_image'] is not None:
                        display_frame = result['pose_result']['output_image']
                    else:
                        display_frame = frame
                    
                    # 顯示當前步驟和動作
                    cv2.putText(display_frame, f"Step: {self.current_step + 1}/{len(self.sop_steps)}",
                               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(display_frame, f"Action: {result['sop_result']['detected_action']}",
                               (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
                    cv2.putText(display_frame, f"Confidence: {result['sop_result']['confidence']:.2f}",
                               (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

                    # 螺絲計數顯示
                    screw_color = (0, 255, 0) if result['screw_count'] <= self.target_screw_count else (0, 0, 255)
                    cv2.putText(display_frame, f"Screws: {result['screw_count']}/{self.target_screw_count}",
                               (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, screw_color, 2)

                    # 時間顯示
                    cv2.putText(display_frame, f"Time: {result['current_time']:.1f}s",
                               (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

                    # 錯誤計數
                    cv2.putText(display_frame, f"Errors: {self.error_count}",
                               (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

                    # 螺絲完成狀態
                    if result['screw_complete']:
                        cv2.putText(display_frame, "SCREWS COMPLETE!",
                                   (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)
                    
                    # 顯示影像
                    cv2.imshow('Assembly SOP Monitor', display_frame)
                
                # 檢查退出鍵
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
        except KeyboardInterrupt:
            print("監控中斷")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self.save_logs()
            print(f"監控結束。總錯誤數: {self.error_count}")

# 使用範例
if __name__ == "__main__":
    # 建立監控系統
    monitor = AssemblySOPMonitor(
        model_folder="models/",  # 請修改為您的 OpenPose 模型路徑
        log_file="assembly_sop_log.json",
        sop_config_file="sop_config_螺絲專用.json"  # 專注於螺絲檢測
    )
    
    # 選擇輸入源
    # 使用您的實際影片檔案
    monitor.run_video_monitoring("1.mp4")

    # 或使用攝像頭
    # monitor.run_video_monitoring(0)