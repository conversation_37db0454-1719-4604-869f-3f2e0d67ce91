input: "image"
input_dim: 1
input_dim: 3
input_dim: 1 # This value will be defined at runtime
input_dim: 1 # This value will be defined at runtime
layer {
  name: "conv1_1"
  type: "Convolution"
  bottom: "image"
  top: "conv1_1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 64
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu1_1"
  type: "ReLU"
  bottom: "conv1_1"
  top: "conv1_1"
}
layer {
  name: "conv1_2"
  type: "Convolution"
  bottom: "conv1_1"
  top: "conv1_2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 64
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu1_2"
  type: "ReLU"
  bottom: "conv1_2"
  top: "conv1_2"
}
layer {
  name: "pool1_stage1"
  type: "Pooling"
  bottom: "conv1_2"
  top: "pool1_stage1"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv2_1"
  type: "Convolution"
  bottom: "pool1_stage1"
  top: "conv2_1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu2_1"
  type: "ReLU"
  bottom: "conv2_1"
  top: "conv2_1"
}
layer {
  name: "conv2_2"
  type: "Convolution"
  bottom: "conv2_1"
  top: "conv2_2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu2_2"
  type: "ReLU"
  bottom: "conv2_2"
  top: "conv2_2"
}
layer {
  name: "pool2_stage1"
  type: "Pooling"
  bottom: "conv2_2"
  top: "pool2_stage1"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv3_1"
  type: "Convolution"
  bottom: "pool2_stage1"
  top: "conv3_1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu3_1"
  type: "ReLU"
  bottom: "conv3_1"
  top: "conv3_1"
}
layer {
  name: "conv3_2"
  type: "Convolution"
  bottom: "conv3_1"
  top: "conv3_2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu3_2"
  type: "ReLU"
  bottom: "conv3_2"
  top: "conv3_2"
}
layer {
  name: "conv3_3"
  type: "Convolution"
  bottom: "conv3_2"
  top: "conv3_3"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu3_3"
  type: "ReLU"
  bottom: "conv3_3"
  top: "conv3_3"
}
layer {
  name: "conv3_4"
  type: "Convolution"
  bottom: "conv3_3"
  top: "conv3_4"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu3_4"
  type: "ReLU"
  bottom: "conv3_4"
  top: "conv3_4"
}
layer {
  name: "pool3_stage1"
  type: "Pooling"
  bottom: "conv3_4"
  top: "pool3_stage1"
  pooling_param {
    pool: MAX
    kernel_size: 2
    stride: 2
  }
}
layer {
  name: "conv4_1"
  type: "Convolution"
  bottom: "pool3_stage1"
  top: "conv4_1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu4_1"
  type: "ReLU"
  bottom: "conv4_1"
  top: "conv4_1"
}
layer {
  name: "conv4_2"
  type: "Convolution"
  bottom: "conv4_1"
  top: "conv4_2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu4_2"
  type: "ReLU"
  bottom: "conv4_2"
  top: "conv4_2"
}
layer {
  name: "conv4_3_CPM"
  type: "Convolution"
  bottom: "conv4_2"
  top: "conv4_3_CPM"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 256
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu4_3_CPM"
  type: "ReLU"
  bottom: "conv4_3_CPM"
  top: "conv4_3_CPM"
}
layer {
  name: "conv4_4_CPM"
  type: "Convolution"
  bottom: "conv4_3_CPM"
  top: "conv4_4_CPM"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu4_4_CPM"
  type: "ReLU"
  bottom: "conv4_4_CPM"
  top: "conv4_4_CPM"
}
layer {
  name: "conv5_1_CPM_L1"
  type: "Convolution"
  bottom: "conv4_4_CPM"
  top: "conv5_1_CPM_L1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_1_CPM_L1"
  type: "ReLU"
  bottom: "conv5_1_CPM_L1"
  top: "conv5_1_CPM_L1"
}
layer {
  name: "conv5_1_CPM_L2"
  type: "Convolution"
  bottom: "conv4_4_CPM"
  top: "conv5_1_CPM_L2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_1_CPM_L2"
  type: "ReLU"
  bottom: "conv5_1_CPM_L2"
  top: "conv5_1_CPM_L2"
}
layer {
  name: "conv5_2_CPM_L1"
  type: "Convolution"
  bottom: "conv5_1_CPM_L1"
  top: "conv5_2_CPM_L1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_2_CPM_L1"
  type: "ReLU"
  bottom: "conv5_2_CPM_L1"
  top: "conv5_2_CPM_L1"
}
layer {
  name: "conv5_2_CPM_L2"
  type: "Convolution"
  bottom: "conv5_1_CPM_L2"
  top: "conv5_2_CPM_L2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_2_CPM_L2"
  type: "ReLU"
  bottom: "conv5_2_CPM_L2"
  top: "conv5_2_CPM_L2"
}
layer {
  name: "conv5_3_CPM_L1"
  type: "Convolution"
  bottom: "conv5_2_CPM_L1"
  top: "conv5_3_CPM_L1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_3_CPM_L1"
  type: "ReLU"
  bottom: "conv5_3_CPM_L1"
  top: "conv5_3_CPM_L1"
}
layer {
  name: "conv5_3_CPM_L2"
  type: "Convolution"
  bottom: "conv5_2_CPM_L2"
  top: "conv5_3_CPM_L2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 1
    kernel_size: 3
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_3_CPM_L2"
  type: "ReLU"
  bottom: "conv5_3_CPM_L2"
  top: "conv5_3_CPM_L2"
}
layer {
  name: "conv5_4_CPM_L1"
  type: "Convolution"
  bottom: "conv5_3_CPM_L1"
  top: "conv5_4_CPM_L1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_4_CPM_L1"
  type: "ReLU"
  bottom: "conv5_4_CPM_L1"
  top: "conv5_4_CPM_L1"
}
layer {
  name: "conv5_4_CPM_L2"
  type: "Convolution"
  bottom: "conv5_3_CPM_L2"
  top: "conv5_4_CPM_L2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 512
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "relu5_4_CPM_L2"
  type: "ReLU"
  bottom: "conv5_4_CPM_L2"
  top: "conv5_4_CPM_L2"
}
layer {
  name: "conv5_5_CPM_L1"
  type: "Convolution"
  bottom: "conv5_4_CPM_L1"
  top: "conv5_5_CPM_L1"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "conv5_5_CPM_L2"
  type: "Convolution"
  bottom: "conv5_4_CPM_L2"
  top: "conv5_5_CPM_L2"
  param {
    lr_mult: 1.0
    decay_mult: 1
  }
  param {
    lr_mult: 2.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage2"
  type: "Concat"
  bottom: "conv5_5_CPM_L1"
  bottom: "conv5_5_CPM_L2"
  bottom: "conv4_4_CPM"
  top: "concat_stage2"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage2_L1"
  type: "Convolution"
  bottom: "concat_stage2"
  top: "Mconv1_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage2_L1"
  type: "ReLU"
  bottom: "Mconv1_stage2_L1"
  top: "Mconv1_stage2_L1"
}
layer {
  name: "Mconv1_stage2_L2"
  type: "Convolution"
  bottom: "concat_stage2"
  top: "Mconv1_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage2_L2"
  type: "ReLU"
  bottom: "Mconv1_stage2_L2"
  top: "Mconv1_stage2_L2"
}
layer {
  name: "Mconv2_stage2_L1"
  type: "Convolution"
  bottom: "Mconv1_stage2_L1"
  top: "Mconv2_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage2_L1"
  type: "ReLU"
  bottom: "Mconv2_stage2_L1"
  top: "Mconv2_stage2_L1"
}
layer {
  name: "Mconv2_stage2_L2"
  type: "Convolution"
  bottom: "Mconv1_stage2_L2"
  top: "Mconv2_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage2_L2"
  type: "ReLU"
  bottom: "Mconv2_stage2_L2"
  top: "Mconv2_stage2_L2"
}
layer {
  name: "Mconv3_stage2_L1"
  type: "Convolution"
  bottom: "Mconv2_stage2_L1"
  top: "Mconv3_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage2_L1"
  type: "ReLU"
  bottom: "Mconv3_stage2_L1"
  top: "Mconv3_stage2_L1"
}
layer {
  name: "Mconv3_stage2_L2"
  type: "Convolution"
  bottom: "Mconv2_stage2_L2"
  top: "Mconv3_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage2_L2"
  type: "ReLU"
  bottom: "Mconv3_stage2_L2"
  top: "Mconv3_stage2_L2"
}
layer {
  name: "Mconv4_stage2_L1"
  type: "Convolution"
  bottom: "Mconv3_stage2_L1"
  top: "Mconv4_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage2_L1"
  type: "ReLU"
  bottom: "Mconv4_stage2_L1"
  top: "Mconv4_stage2_L1"
}
layer {
  name: "Mconv4_stage2_L2"
  type: "Convolution"
  bottom: "Mconv3_stage2_L2"
  top: "Mconv4_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage2_L2"
  type: "ReLU"
  bottom: "Mconv4_stage2_L2"
  top: "Mconv4_stage2_L2"
}
layer {
  name: "Mconv5_stage2_L1"
  type: "Convolution"
  bottom: "Mconv4_stage2_L1"
  top: "Mconv5_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage2_L1"
  type: "ReLU"
  bottom: "Mconv5_stage2_L1"
  top: "Mconv5_stage2_L1"
}
layer {
  name: "Mconv5_stage2_L2"
  type: "Convolution"
  bottom: "Mconv4_stage2_L2"
  top: "Mconv5_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage2_L2"
  type: "ReLU"
  bottom: "Mconv5_stage2_L2"
  top: "Mconv5_stage2_L2"
}
layer {
  name: "Mconv6_stage2_L1"
  type: "Convolution"
  bottom: "Mconv5_stage2_L1"
  top: "Mconv6_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage2_L1"
  type: "ReLU"
  bottom: "Mconv6_stage2_L1"
  top: "Mconv6_stage2_L1"
}
layer {
  name: "Mconv6_stage2_L2"
  type: "Convolution"
  bottom: "Mconv5_stage2_L2"
  top: "Mconv6_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage2_L2"
  type: "ReLU"
  bottom: "Mconv6_stage2_L2"
  top: "Mconv6_stage2_L2"
}
layer {
  name: "Mconv7_stage2_L1"
  type: "Convolution"
  bottom: "Mconv6_stage2_L1"
  top: "Mconv7_stage2_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv7_stage2_L2"
  type: "Convolution"
  bottom: "Mconv6_stage2_L2"
  top: "Mconv7_stage2_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage3"
  type: "Concat"
  bottom: "Mconv7_stage2_L1"
  bottom: "Mconv7_stage2_L2"
  bottom: "conv4_4_CPM"
  top: "concat_stage3"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage3_L1"
  type: "Convolution"
  bottom: "concat_stage3"
  top: "Mconv1_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage3_L1"
  type: "ReLU"
  bottom: "Mconv1_stage3_L1"
  top: "Mconv1_stage3_L1"
}
layer {
  name: "Mconv1_stage3_L2"
  type: "Convolution"
  bottom: "concat_stage3"
  top: "Mconv1_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage3_L2"
  type: "ReLU"
  bottom: "Mconv1_stage3_L2"
  top: "Mconv1_stage3_L2"
}
layer {
  name: "Mconv2_stage3_L1"
  type: "Convolution"
  bottom: "Mconv1_stage3_L1"
  top: "Mconv2_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage3_L1"
  type: "ReLU"
  bottom: "Mconv2_stage3_L1"
  top: "Mconv2_stage3_L1"
}
layer {
  name: "Mconv2_stage3_L2"
  type: "Convolution"
  bottom: "Mconv1_stage3_L2"
  top: "Mconv2_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage3_L2"
  type: "ReLU"
  bottom: "Mconv2_stage3_L2"
  top: "Mconv2_stage3_L2"
}
layer {
  name: "Mconv3_stage3_L1"
  type: "Convolution"
  bottom: "Mconv2_stage3_L1"
  top: "Mconv3_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage3_L1"
  type: "ReLU"
  bottom: "Mconv3_stage3_L1"
  top: "Mconv3_stage3_L1"
}
layer {
  name: "Mconv3_stage3_L2"
  type: "Convolution"
  bottom: "Mconv2_stage3_L2"
  top: "Mconv3_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage3_L2"
  type: "ReLU"
  bottom: "Mconv3_stage3_L2"
  top: "Mconv3_stage3_L2"
}
layer {
  name: "Mconv4_stage3_L1"
  type: "Convolution"
  bottom: "Mconv3_stage3_L1"
  top: "Mconv4_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage3_L1"
  type: "ReLU"
  bottom: "Mconv4_stage3_L1"
  top: "Mconv4_stage3_L1"
}
layer {
  name: "Mconv4_stage3_L2"
  type: "Convolution"
  bottom: "Mconv3_stage3_L2"
  top: "Mconv4_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage3_L2"
  type: "ReLU"
  bottom: "Mconv4_stage3_L2"
  top: "Mconv4_stage3_L2"
}
layer {
  name: "Mconv5_stage3_L1"
  type: "Convolution"
  bottom: "Mconv4_stage3_L1"
  top: "Mconv5_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage3_L1"
  type: "ReLU"
  bottom: "Mconv5_stage3_L1"
  top: "Mconv5_stage3_L1"
}
layer {
  name: "Mconv5_stage3_L2"
  type: "Convolution"
  bottom: "Mconv4_stage3_L2"
  top: "Mconv5_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage3_L2"
  type: "ReLU"
  bottom: "Mconv5_stage3_L2"
  top: "Mconv5_stage3_L2"
}
layer {
  name: "Mconv6_stage3_L1"
  type: "Convolution"
  bottom: "Mconv5_stage3_L1"
  top: "Mconv6_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage3_L1"
  type: "ReLU"
  bottom: "Mconv6_stage3_L1"
  top: "Mconv6_stage3_L1"
}
layer {
  name: "Mconv6_stage3_L2"
  type: "Convolution"
  bottom: "Mconv5_stage3_L2"
  top: "Mconv6_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage3_L2"
  type: "ReLU"
  bottom: "Mconv6_stage3_L2"
  top: "Mconv6_stage3_L2"
}
layer {
  name: "Mconv7_stage3_L1"
  type: "Convolution"
  bottom: "Mconv6_stage3_L1"
  top: "Mconv7_stage3_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv7_stage3_L2"
  type: "Convolution"
  bottom: "Mconv6_stage3_L2"
  top: "Mconv7_stage3_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage4"
  type: "Concat"
  bottom: "Mconv7_stage3_L1"
  bottom: "Mconv7_stage3_L2"
  bottom: "conv4_4_CPM"
  top: "concat_stage4"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage4_L1"
  type: "Convolution"
  bottom: "concat_stage4"
  top: "Mconv1_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage4_L1"
  type: "ReLU"
  bottom: "Mconv1_stage4_L1"
  top: "Mconv1_stage4_L1"
}
layer {
  name: "Mconv1_stage4_L2"
  type: "Convolution"
  bottom: "concat_stage4"
  top: "Mconv1_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage4_L2"
  type: "ReLU"
  bottom: "Mconv1_stage4_L2"
  top: "Mconv1_stage4_L2"
}
layer {
  name: "Mconv2_stage4_L1"
  type: "Convolution"
  bottom: "Mconv1_stage4_L1"
  top: "Mconv2_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage4_L1"
  type: "ReLU"
  bottom: "Mconv2_stage4_L1"
  top: "Mconv2_stage4_L1"
}
layer {
  name: "Mconv2_stage4_L2"
  type: "Convolution"
  bottom: "Mconv1_stage4_L2"
  top: "Mconv2_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage4_L2"
  type: "ReLU"
  bottom: "Mconv2_stage4_L2"
  top: "Mconv2_stage4_L2"
}
layer {
  name: "Mconv3_stage4_L1"
  type: "Convolution"
  bottom: "Mconv2_stage4_L1"
  top: "Mconv3_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage4_L1"
  type: "ReLU"
  bottom: "Mconv3_stage4_L1"
  top: "Mconv3_stage4_L1"
}
layer {
  name: "Mconv3_stage4_L2"
  type: "Convolution"
  bottom: "Mconv2_stage4_L2"
  top: "Mconv3_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage4_L2"
  type: "ReLU"
  bottom: "Mconv3_stage4_L2"
  top: "Mconv3_stage4_L2"
}
layer {
  name: "Mconv4_stage4_L1"
  type: "Convolution"
  bottom: "Mconv3_stage4_L1"
  top: "Mconv4_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage4_L1"
  type: "ReLU"
  bottom: "Mconv4_stage4_L1"
  top: "Mconv4_stage4_L1"
}
layer {
  name: "Mconv4_stage4_L2"
  type: "Convolution"
  bottom: "Mconv3_stage4_L2"
  top: "Mconv4_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage4_L2"
  type: "ReLU"
  bottom: "Mconv4_stage4_L2"
  top: "Mconv4_stage4_L2"
}
layer {
  name: "Mconv5_stage4_L1"
  type: "Convolution"
  bottom: "Mconv4_stage4_L1"
  top: "Mconv5_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage4_L1"
  type: "ReLU"
  bottom: "Mconv5_stage4_L1"
  top: "Mconv5_stage4_L1"
}
layer {
  name: "Mconv5_stage4_L2"
  type: "Convolution"
  bottom: "Mconv4_stage4_L2"
  top: "Mconv5_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage4_L2"
  type: "ReLU"
  bottom: "Mconv5_stage4_L2"
  top: "Mconv5_stage4_L2"
}
layer {
  name: "Mconv6_stage4_L1"
  type: "Convolution"
  bottom: "Mconv5_stage4_L1"
  top: "Mconv6_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage4_L1"
  type: "ReLU"
  bottom: "Mconv6_stage4_L1"
  top: "Mconv6_stage4_L1"
}
layer {
  name: "Mconv6_stage4_L2"
  type: "Convolution"
  bottom: "Mconv5_stage4_L2"
  top: "Mconv6_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage4_L2"
  type: "ReLU"
  bottom: "Mconv6_stage4_L2"
  top: "Mconv6_stage4_L2"
}
layer {
  name: "Mconv7_stage4_L1"
  type: "Convolution"
  bottom: "Mconv6_stage4_L1"
  top: "Mconv7_stage4_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv7_stage4_L2"
  type: "Convolution"
  bottom: "Mconv6_stage4_L2"
  top: "Mconv7_stage4_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage5"
  type: "Concat"
  bottom: "Mconv7_stage4_L1"
  bottom: "Mconv7_stage4_L2"
  bottom: "conv4_4_CPM"
  top: "concat_stage5"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage5_L1"
  type: "Convolution"
  bottom: "concat_stage5"
  top: "Mconv1_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage5_L1"
  type: "ReLU"
  bottom: "Mconv1_stage5_L1"
  top: "Mconv1_stage5_L1"
}
layer {
  name: "Mconv1_stage5_L2"
  type: "Convolution"
  bottom: "concat_stage5"
  top: "Mconv1_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage5_L2"
  type: "ReLU"
  bottom: "Mconv1_stage5_L2"
  top: "Mconv1_stage5_L2"
}
layer {
  name: "Mconv2_stage5_L1"
  type: "Convolution"
  bottom: "Mconv1_stage5_L1"
  top: "Mconv2_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage5_L1"
  type: "ReLU"
  bottom: "Mconv2_stage5_L1"
  top: "Mconv2_stage5_L1"
}
layer {
  name: "Mconv2_stage5_L2"
  type: "Convolution"
  bottom: "Mconv1_stage5_L2"
  top: "Mconv2_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage5_L2"
  type: "ReLU"
  bottom: "Mconv2_stage5_L2"
  top: "Mconv2_stage5_L2"
}
layer {
  name: "Mconv3_stage5_L1"
  type: "Convolution"
  bottom: "Mconv2_stage5_L1"
  top: "Mconv3_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage5_L1"
  type: "ReLU"
  bottom: "Mconv3_stage5_L1"
  top: "Mconv3_stage5_L1"
}
layer {
  name: "Mconv3_stage5_L2"
  type: "Convolution"
  bottom: "Mconv2_stage5_L2"
  top: "Mconv3_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage5_L2"
  type: "ReLU"
  bottom: "Mconv3_stage5_L2"
  top: "Mconv3_stage5_L2"
}
layer {
  name: "Mconv4_stage5_L1"
  type: "Convolution"
  bottom: "Mconv3_stage5_L1"
  top: "Mconv4_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage5_L1"
  type: "ReLU"
  bottom: "Mconv4_stage5_L1"
  top: "Mconv4_stage5_L1"
}
layer {
  name: "Mconv4_stage5_L2"
  type: "Convolution"
  bottom: "Mconv3_stage5_L2"
  top: "Mconv4_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage5_L2"
  type: "ReLU"
  bottom: "Mconv4_stage5_L2"
  top: "Mconv4_stage5_L2"
}
layer {
  name: "Mconv5_stage5_L1"
  type: "Convolution"
  bottom: "Mconv4_stage5_L1"
  top: "Mconv5_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage5_L1"
  type: "ReLU"
  bottom: "Mconv5_stage5_L1"
  top: "Mconv5_stage5_L1"
}
layer {
  name: "Mconv5_stage5_L2"
  type: "Convolution"
  bottom: "Mconv4_stage5_L2"
  top: "Mconv5_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage5_L2"
  type: "ReLU"
  bottom: "Mconv5_stage5_L2"
  top: "Mconv5_stage5_L2"
}
layer {
  name: "Mconv6_stage5_L1"
  type: "Convolution"
  bottom: "Mconv5_stage5_L1"
  top: "Mconv6_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage5_L1"
  type: "ReLU"
  bottom: "Mconv6_stage5_L1"
  top: "Mconv6_stage5_L1"
}
layer {
  name: "Mconv6_stage5_L2"
  type: "Convolution"
  bottom: "Mconv5_stage5_L2"
  top: "Mconv6_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage5_L2"
  type: "ReLU"
  bottom: "Mconv6_stage5_L2"
  top: "Mconv6_stage5_L2"
}
layer {
  name: "Mconv7_stage5_L1"
  type: "Convolution"
  bottom: "Mconv6_stage5_L1"
  top: "Mconv7_stage5_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv7_stage5_L2"
  type: "Convolution"
  bottom: "Mconv6_stage5_L2"
  top: "Mconv7_stage5_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage6"
  type: "Concat"
  bottom: "Mconv7_stage5_L1"
  bottom: "Mconv7_stage5_L2"
  bottom: "conv4_4_CPM"
  top: "concat_stage6"
  concat_param {
    axis: 1
  }
}
layer {
  name: "Mconv1_stage6_L1"
  type: "Convolution"
  bottom: "concat_stage6"
  top: "Mconv1_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage6_L1"
  type: "ReLU"
  bottom: "Mconv1_stage6_L1"
  top: "Mconv1_stage6_L1"
}
layer {
  name: "Mconv1_stage6_L2"
  type: "Convolution"
  bottom: "concat_stage6"
  top: "Mconv1_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu1_stage6_L2"
  type: "ReLU"
  bottom: "Mconv1_stage6_L2"
  top: "Mconv1_stage6_L2"
}
layer {
  name: "Mconv2_stage6_L1"
  type: "Convolution"
  bottom: "Mconv1_stage6_L1"
  top: "Mconv2_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage6_L1"
  type: "ReLU"
  bottom: "Mconv2_stage6_L1"
  top: "Mconv2_stage6_L1"
}
layer {
  name: "Mconv2_stage6_L2"
  type: "Convolution"
  bottom: "Mconv1_stage6_L2"
  top: "Mconv2_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu2_stage6_L2"
  type: "ReLU"
  bottom: "Mconv2_stage6_L2"
  top: "Mconv2_stage6_L2"
}
layer {
  name: "Mconv3_stage6_L1"
  type: "Convolution"
  bottom: "Mconv2_stage6_L1"
  top: "Mconv3_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage6_L1"
  type: "ReLU"
  bottom: "Mconv3_stage6_L1"
  top: "Mconv3_stage6_L1"
}
layer {
  name: "Mconv3_stage6_L2"
  type: "Convolution"
  bottom: "Mconv2_stage6_L2"
  top: "Mconv3_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu3_stage6_L2"
  type: "ReLU"
  bottom: "Mconv3_stage6_L2"
  top: "Mconv3_stage6_L2"
}
layer {
  name: "Mconv4_stage6_L1"
  type: "Convolution"
  bottom: "Mconv3_stage6_L1"
  top: "Mconv4_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage6_L1"
  type: "ReLU"
  bottom: "Mconv4_stage6_L1"
  top: "Mconv4_stage6_L1"
}
layer {
  name: "Mconv4_stage6_L2"
  type: "Convolution"
  bottom: "Mconv3_stage6_L2"
  top: "Mconv4_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu4_stage6_L2"
  type: "ReLU"
  bottom: "Mconv4_stage6_L2"
  top: "Mconv4_stage6_L2"
}
layer {
  name: "Mconv5_stage6_L1"
  type: "Convolution"
  bottom: "Mconv4_stage6_L1"
  top: "Mconv5_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage6_L1"
  type: "ReLU"
  bottom: "Mconv5_stage6_L1"
  top: "Mconv5_stage6_L1"
}
layer {
  name: "Mconv5_stage6_L2"
  type: "Convolution"
  bottom: "Mconv4_stage6_L2"
  top: "Mconv5_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 3
    kernel_size: 7
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu5_stage6_L2"
  type: "ReLU"
  bottom: "Mconv5_stage6_L2"
  top: "Mconv5_stage6_L2"
}
layer {
  name: "Mconv6_stage6_L1"
  type: "Convolution"
  bottom: "Mconv5_stage6_L1"
  top: "Mconv6_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage6_L1"
  type: "ReLU"
  bottom: "Mconv6_stage6_L1"
  top: "Mconv6_stage6_L1"
}
layer {
  name: "Mconv6_stage6_L2"
  type: "Convolution"
  bottom: "Mconv5_stage6_L2"
  top: "Mconv6_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 128
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mrelu6_stage6_L2"
  type: "ReLU"
  bottom: "Mconv6_stage6_L2"
  top: "Mconv6_stage6_L2"
}
layer {
  name: "Mconv7_stage6_L1"
  type: "Convolution"
  bottom: "Mconv6_stage6_L1"
  top: "Mconv7_stage6_L1"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 38
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "Mconv7_stage6_L2"
  type: "Convolution"
  bottom: "Mconv6_stage6_L2"
  top: "Mconv7_stage6_L2"
  param {
    lr_mult: 4.0
    decay_mult: 1
  }
  param {
    lr_mult: 8.0
    decay_mult: 0
  }
  convolution_param {
    num_output: 19
    pad: 0
    kernel_size: 1
    weight_filler {
      type: "gaussian"
      std: 0.01
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "concat_stage7"
  type: "Concat"
  bottom: "Mconv7_stage6_L2"
  bottom: "Mconv7_stage6_L1"
  # top: "concat_stage7"
  top: "net_output"
  concat_param {
    axis: 1
  }
}
