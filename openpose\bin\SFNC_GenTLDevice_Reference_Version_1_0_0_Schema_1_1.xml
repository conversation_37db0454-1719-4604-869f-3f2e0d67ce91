<?xml version="1.0" encoding="UTF-8"?>
<RegisterDescription xmlns="http://www.genicam.org/GenApi/Version_1_1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.genicam.org/GenApi/Version_1_1 http://www.genicam.org/GenApi/GenApiSchema_Version_1_1.xsd" ModelName="SFNC_Camera" VendorName="Generic" ToolTip="Reference SFNC camera XML (autogenerated)" StandardNameSpace="None" SchemaMajorVersion="1" SchemaMinorVersion="1" SchemaSubMinorVersion="0" MajorVersion="1" MinorVersion="0" SubMinorVersion="0" ProductGuid="11111111-**************-************" VersionGuid="AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE">
  <Group Comment="RootCategory">
    <Category Name="Root" NameSpace="Standard">
      <!-- Mandatory element -->
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceInformation</pFeature>
      <pFeature>DeviceControl</pFeature>
      <pFeature>StreamEnumeration</pFeature>
    </Category>
  </Group>
  <Group Comment="SubCategories">
    <Category Name="DeviceInformation" NameSpace="Standard">
      <!-- Recommended element -->
      <ToolTip>Category that contains all Device Information features of the Device module.</ToolTip>
      <Description>Category that contains all Device Information features of the Device module.</Description>
      <DisplayName>Device Information</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceID</pFeature>
      <pFeature>DeviceSerialNumber</pFeature>
      <pFeature>DeviceUserID</pFeature>
      <pFeature>DeviceVendorName</pFeature>
      <pFeature>DeviceModelName</pFeature>
      <pFeature>DeviceFamilyName</pFeature>
      <pFeature>DeviceVersion</pFeature>
      <pFeature>DeviceManufacturerInfo</pFeature>
      <pFeature>DeviceType</pFeature>
      <pFeature>DeviceDisplayName</pFeature>
      <pFeature>DeviceAccessStatus</pFeature>
      <pFeature>DeviceChunkDataFormat</pFeature>
      <pFeature>DeviceEventDataFormat</pFeature>
      <pFeature>GevDeviceIPAddress</pFeature>
      <pFeature>GevDeviceSubnetMask</pFeature>
      <pFeature>GevDeviceMACAddress</pFeature>
      <pFeature>GevDeviceGateway</pFeature>
    </Category>
    <Category Name="DeviceControl" NameSpace="Standard">
      <ToolTip>Category that contains all Device Control features of the Device module.</ToolTip>
      <Description>Category that contains all Device Control features of the Device module.</Description>
      <DisplayName>Device Control</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>DeviceEndianessMechanism</pFeature>
    </Category>
    <Category Name="StreamEnumeration" NameSpace="Standard">
      <ToolTip>Category that contains all Stream Enumeration features of the Device module.</ToolTip>
      <Description>Category that contains all Stream Enumeration features of the Device module.</Description>
      <DisplayName>Stream Enumeration</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <pFeature>StreamSelector</pFeature>
      <pFeature>StreamID</pFeature>
    </Category>
  </Group>
  <Group Comment="DeviceInformation">
    <String Name="DeviceID" NameSpace="Standard">
      <ToolTip>Interface-wide unique identifier of this device.</ToolTip>
      <Description>Interface-wide unique identifier of this device.</Description>
      <DisplayName>Device ID  </DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceSerialNumber" NameSpace="Standard">
      <ToolTip>Serial number of the remote device.</ToolTip>
      <Description>Serial number of the remote device.</Description>
      <DisplayName>Device Serial Number</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceUserID" NameSpace="Standard">
      <ToolTip>User-programmable device identifier of the remote device.</ToolTip>
      <Description>User-programmable device identifier of the remote device.</Description>
      <DisplayName>Device User ID</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceVendorName" NameSpace="Standard">
      <ToolTip>Name of the remote device vendor.</ToolTip>
      <Description>Name of the remote device vendor.</Description>
      <DisplayName>Device Vendor Name  </DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceModelName" NameSpace="Standard">
      <ToolTip>Name of the remote device model.</ToolTip>
      <Description>Name of the remote device model.</Description>
      <DisplayName>Device Model Name</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceFamilyName" NameSpace="Standard">
      <ToolTip>Name of the product family of the remote device model.</ToolTip>
      <Description>Name of the product family of the remote device model.</Description>
      <DisplayName>Device Family Name</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceVersion" NameSpace="Standard">
      <ToolTip>Name of the version of the remote device model.</ToolTip>
      <Description>Name of the version of the remote device model.</Description>
      <DisplayName>Device Version</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <String Name="DeviceManufacturerInfo" NameSpace="Standard">
      <ToolTip>Manufacturer information about the remote device.</ToolTip>
      <Description>Manufacturer information about the remote device.</Description>
      <DisplayName>Device Manufacturer Info</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <Enumeration Name="DeviceType" NameSpace="Standard">
      <ToolTip>Transport layer type of the device.</ToolTip>
      <Description>Transport layer type of the device.</Description>
      <DisplayName>Device Type</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <EnumEntry Name="GigEVision" NameSpace="Standard">
        <ToolTip>GigE Vision.</ToolTip>
        <Description>GigE Vision</Description>
        <DisplayName>Gig E Vision</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="CameraLink" NameSpace="Standard">
        <ToolTip>Camera Link.</ToolTip>
        <Description>Camera Link</Description>
        <DisplayName>Camera Link</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="CameraLinkHS" NameSpace="Standard">
        <ToolTip>Camera Link High Speed.</ToolTip>
        <Description>Camera Link High Speed</Description>
        <DisplayName>Camera Link HS</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <EnumEntry Name="CoaXPress" NameSpace="Standard">
        <ToolTip>CoaXPress.</ToolTip>
        <Description>CoaXPress</Description>
        <DisplayName>Coa X Press</DisplayName>
        <Value>3</Value>
      </EnumEntry>
      <EnumEntry Name="USB3Vision" NameSpace="Standard">
        <ToolTip>USB3 Vision.</ToolTip>
        <Description>USB3 Vision</Description>
        <DisplayName>USB 3 Vision</DisplayName>
        <Value>4</Value>
      </EnumEntry>
      <EnumEntry Name="Custom" NameSpace="Standard">
        <ToolTip>Custom transport layer.</ToolTip>
        <Description>Custom transport layer</Description>
        <DisplayName>Custom</DisplayName>
        <Value>5</Value>
      </EnumEntry>
      <Value>0</Value>
    </Enumeration>
    <String Name="DeviceDisplayName" NameSpace="Standard">
      <ToolTip>User readable name of the device.</ToolTip>
      <Description>User readable name of the device. If this is not defined in the device this should be "VENDOR MODEL (ID)".</Description>
      <DisplayName>Device Display Name</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
    <StringReg Name="DeviceInstanceId">
      <Visibility>Invisible</Visibility>
      <Address>0x4000</Address>
      <Length>512</Length>
      <AccessMode>RO</AccessMode>
      <pPort>Device</pPort>
    </StringReg>
    <Enumeration Name="DeviceAccessStatus" NameSpace="Standard">
      <ToolTip>Gets the access status the transport layer Producer has on the device:.</ToolTip>
      <Description>Gets the access status the transport layer Producer has on the device:</Description>
      <DisplayName>Device Access Status</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <EnumEntry Name="Unknown" NameSpace="Standard">
        <ToolTip>Unknown status.</ToolTip>
        <Description>Unknown status</Description>
        <DisplayName>Unknown</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="ReadWrite" NameSpace="Standard">
        <ToolTip>Full access.</ToolTip>
        <Description>Full access</Description>
        <DisplayName>Read Write</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="ReadOnly" NameSpace="Standard">
        <ToolTip>Read-only access.</ToolTip>
        <Description>Read-only access</Description>
        <DisplayName>Read Only</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <EnumEntry Name="NoAccess" NameSpace="Standard">
        <ToolTip>Non-available devices.</ToolTip>
        <Description>Non-available devices</Description>
        <DisplayName>No Access</DisplayName>
        <Value>3</Value>
      </EnumEntry>
      <Value>0</Value>
    </Enumeration>
    <Enumeration Name="DeviceChunkDataFormat" NameSpace="Standard">
      <ToolTip>Chunk data format used by the device.</ToolTip>
      <Description>Chunk data format used by the device. This information allows devices based on other technologies or protocols than "standard" ones such as GigE Vision to inform the GenTL Consumer about the chunk data layout they use.</Description>
      <DisplayName>Device Chunk Data Format</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <EnumEntry Name="None" NameSpace="Standard">
        <ToolTip>None</ToolTip>
        <Description>None</Description>
        <DisplayName>None</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="GigEVision" NameSpace="Standard">
        <ToolTip>Gig E Vision</ToolTip>
        <Description>Gig E Vision</Description>
        <DisplayName>Gig E Vision</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="Custom" NameSpace="Standard">
        <ToolTip>Custom</ToolTip>
        <Description>Custom</Description>
        <DisplayName>Custom</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <Value>0</Value>
    </Enumeration>
    <Enumeration Name="DeviceEventDataFormat" NameSpace="Standard">
      <ToolTip>Enumeration, informing about the event data format used by the device (meaning the "device events", see event type EVENT_FEATURE_DEVEVENT).</ToolTip>
      <Description>Enumeration, informing about the event data format used by the device (meaning the "device events", see event type EVENT_FEATURE_DEVEVENT). This allows devices based on other technologies or protocols than "standard" ones such as GigE Vision to inform the GenTL Consumer about the event data layout they use.</Description>
      <DisplayName>Device Event Data Format</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <EnumEntry Name="None" NameSpace="Standard">
        <ToolTip>None</ToolTip>
        <Description>None</Description>
        <DisplayName>None</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="GigEVision" NameSpace="Standard">
        <ToolTip>Gig E Vision</ToolTip>
        <Description>Gig E Vision</Description>
        <DisplayName>Gig E Vision</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <EnumEntry Name="Custom" NameSpace="Standard">
        <ToolTip>Custom</ToolTip>
        <Description>Custom</Description>
        <DisplayName>Custom</DisplayName>
        <Value>2</Value>
      </EnumEntry>
      <Value>0</Value>
    </Enumeration>
    <Integer Name="GevDeviceIPAddress" NameSpace="Standard">
      <ToolTip>Current IP address of the GVCP interface of the remote device.</ToolTip>
      <Description>Current IP address of the GVCP interface of the remote device.</Description>
      <DisplayName>Gev Device IP Address</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="GevDeviceSubnetMask" NameSpace="Standard">
      <ToolTip>Current subnet mask of the GVCP interface of the remote device.</ToolTip>
      <Description>Current subnet mask of the GVCP interface of the remote device.</Description>
      <DisplayName>Gev Device Subnet Mask</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="GevDeviceMACAddress" NameSpace="Standard">
      <ToolTip>48-bit MAC address of the GVCP interface of the remote device.</ToolTip>
      <Description>48-bit MAC address of the GVCP interface of the remote device.</Description>
      <DisplayName>Gev Device MAC Address</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
    <Integer Name="GevDeviceGateway" NameSpace="Standard">
      <ToolTip>Current gateway IP address of the GVCP interface of the remote device.</ToolTip>
      <Description>Current gateway IP address of the GVCP interface of the remote device.</Description>
      <DisplayName>Gev Device Gateway</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>0</Value>
    </Integer>
  </Group>
  <Group Comment="DeviceControl">
    <Enumeration Name="DeviceEndianessMechanism" NameSpace="Standard">
      <ToolTip>Identifies the endianness handling mode.</ToolTip>
      <Description>Identifies the endianness handling mode.</Description>
      <DisplayName>Device Endianess Mechanism</DisplayName>
      <Visibility>Expert</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <EnumEntry Name="Legacy" NameSpace="Standard">
        <ToolTip>Handling the device endianness according to GenICam Schema 1.</ToolTip>
        <Description>Handling the device endianness according to GenICam Schema 1.0</Description>
        <DisplayName>Legacy</DisplayName>
        <Value>0</Value>
      </EnumEntry>
      <EnumEntry Name="Standard" NameSpace="Standard">
        <ToolTip>Handling the device endianness according to GenICam Schema 1.</ToolTip>
        <Description>Handling the device endianness according to GenICam Schema 1.1 and later</Description>
        <DisplayName>Standard</DisplayName>
        <Value>1</Value>
      </EnumEntry>
      <Value>0</Value>
    </Enumeration>
  </Group>
  <Group Comment="StreamEnumeration">
    <Integer Name="StreamSelector" NameSpace="Standard">
      <ToolTip>Selector for the different stream channels.</ToolTip>
      <Description>Selector for the different stream channels.The selector is 0-based in order to match the index of the C interface.</Description>
      <DisplayName>Stream Selector</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RW</ImposedAccessMode>
      <Value>0</Value>
      <pSelected>StreamID</pSelected>
    </Integer>
    <String Name="StreamID" NameSpace="Standard">
      <!-- Mandatory element -->
      <ToolTip>Device unique ID for the stream, e.</ToolTip>
      <Description>Device unique ID for the stream, e.g. a GUID.</Description>
      <DisplayName>Stream ID</DisplayName>
      <Visibility>Beginner</Visibility>
      <ImposedAccessMode>RO</ImposedAccessMode>
      <Value>abc</Value>
    </String>
  </Group>
  <Group Comment="SpecialFeatures">
    <Port Name="Device" NameSpace="Standard">
    </Port>
  </Group>
</RegisterDescription>
